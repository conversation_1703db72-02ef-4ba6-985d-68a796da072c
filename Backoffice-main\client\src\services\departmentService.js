import api from '@/api'

export const departmentService = {
  async addDepartment(department) {
    const response = await api.post('/departments', department)
    return response.data
  },
  async getDepartments() {
    const response = await api.get('/departments')
    return response.data
  },
  async getDepartmentById(id) {
    const response = await api.get(`/departments/${id}`)
    return response.data
  },
  async updateDepartment(id, department) {
    const response = await api.put(`/departments/${id}`, department)
    return response.data
  },
  async deleteDepartment(id) {
    const response = await api.delete(`/departments/${id}`)
    return response.data
  },
}
