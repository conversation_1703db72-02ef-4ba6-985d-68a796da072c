# Backoffice System Diagrams

This document contains various Mermaid diagrams that visualize the structure and relationships of the Backoffice backend system.

## Database Schema

```mermaid
erDiagram
    USER {
        string _id PK
        string email
        string password
        boolean isAdmin
        string firstName
        string lastName
        string profileImage
        date createdAt
        date updatedAt
    }

    EMPLOYEE {
        string _id PK
        string firstName
        string lastName
        string gender
        date birthDate
        string phone
        string email
        string address
        string department FK
        date hireDate
        string status
        string createdBy FK
        date createdAt
    }

    DEPARTMENT {
        string _id PK
        string name
        string description
        string managerId FK
        date createdAt
    }

    CONTRACT {
        string _id PK
        string employeeId FK
        string fileUrl
        object fileName
        date uploadedAt
        date expiresAt
        objectId createdBy FK
        number salary
    }

    DEMAND {
        string _id PK
        string employeeId FK
        string type
        date startDate
        date endDate
        string status
        date requestedAt
    }

    ATTENDANCE {
        string _id PK
        string employeeId FK
        date date
        date checkIn
        date checkOut
        string status
    }

    USER ||--o{ EMPLOYEE : "creates"
    DEPARTMENT ||--o{ EMPLOYEE : "has"
    EMPLOYEE ||--o{ CONTRACT : "has"
    EMPLOYEE ||--o{ DEMAND : "requests"
    EMPLOYEE ||--o{ ATTENDANCE : "records"
    USER ||--o{ CONTRACT : "creates"
```

mermaid```
graph TD
A[Express App] --> B[/api/employees]
A --> C[/api/departments]
A --> D[/api/users]
A --> E[/api/contracts]
A --> F[/api/demands]
A --> G[/api/attendances]
A --> H[/api/auth]

    B --> B1[POST /]
    B --> B2[GET /]
    B --> B3[GET /:id]
    B --> B4[PUT /:id]
    B --> B5[DELETE /:id]

    C --> C1[POST /]
    C --> C2[GET /]
    C --> C3[GET /:id]
    C --> C4[PUT /:id]
    C --> C5[DELETE /:id]

    D --> D1[POST /]
    D --> D2[GET /]
    D --> D3[GET /:id]
    D --> D4[PUT /:id]
    D --> D5[DELETE /:id]

    E --> E1[POST /]
    E --> E2[GET /]
    E --> E3[GET /:id]
    E --> E4[PUT /:id]
    E --> E5[DELETE /:id]

    F --> F1[POST /]
    F --> F2[GET /]
    F --> F3[GET /:id]
    F --> F4[PUT /:id]
    F --> F5[DELETE /:id]

    G --> G1[POST /]
    G --> G2[GET /]
    G --> G3[GET /:id]
    G --> G4[PUT /:id]
    G --> G5[DELETE /:id]

```

This Markdown file contains 8 different Mermaid diagrams that visualize various aspects of your Backoffice backend system:

1. Database Schema - Shows the structure and relationships between your MongoDB collections
2. API Routes Structure - Visualizes the API endpoints and their HTTP methods
3. Application Architecture - Shows the high-level architecture of your application
4. MVC Pattern Implementation - Illustrates how the MVC pattern is implemented in your app
5. User Authentication Flow - Sequence diagram showing the authentication process
6. Employee Management Flow - Shows the business logic flow for employee management
7. Data Relationships - Class diagram showing relationships between your data models
8. Backend Component Structure - Detailed view of how components connect in your backend

You can save this file to `/Users/<USER>/Final/Backoffice/backend/docs/system_diagrams.md` and view it in any Markdown viewer that supports Mermaid diagrams (like GitHub, VS Code with Mermaid extension, etc.).
```
