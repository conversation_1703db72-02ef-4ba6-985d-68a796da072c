<template>
  <div class="user-profile-container">
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title text-2xl mb-6">User Profile</h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Profile Image Section -->
          <div class="flex flex-col items-center">
            <ProfileImage :userId="user?._id" v-model:profileImage="user.profileImage"
              :editable="isCurrentUser || isAdmin" />
          </div>

          <!-- User Details Section -->
          <div class="md:col-span-2">
            <!-- User details form or display -->
            <!-- ... -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ProfileImage from '@/components/ProfileImage.vue';
import { useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/authStore';
import { useUserStore } from '@/stores/userStore';
import { push } from 'notivue';

export default {
  name: 'UserProfileView',
  components: {
    ProfileImage
  },
  data() {
    return {
      user: {},
      isLoading: false
    };
  },
  computed: {
    userId() {
      const route = useRoute();
      const authStore = useAuthStore();
      return route.params.id || authStore.user?.id;
    },
    isCurrentUser() {
      const authStore = useAuthStore();
      return authStore.user?.id === this.userId;
    },
    isAdmin() {
      const authStore = useAuthStore();
      return authStore.isAdmin;
    }
  },
  created() {
    this.fetchUserData();
  },
  methods: {
    async fetchUserData() {
      const userStore = useUserStore();
      if (!this.userId) return;
      try {
        this.isLoading = true;
        this.user = await userStore.getUserById(this.userId);
      } catch (error) {
        console.error('Error fetching user:', error);
        push.error({
          title: 'Erreur',
          message: 'Impossible de charger le profil utilisateur',
          type: 'error'
        });
      } finally {
        this.isLoading = false;
      }
    }
  }
};
</script>
