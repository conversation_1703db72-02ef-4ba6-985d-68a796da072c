<template>
    <div class="add-contract-container flex justify-center items-center min-h-[60vh]">
        <div class="card w-full max-w-xl p-6 md:p-10 bg-base-100 shadow-xl rounded-xl">
            <div class="flex items-center mb-6">
                <button @click="$router.back()" class="btn btn-outline btn-sm mr-4">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="card-title text-center flex-1">Ajouter un contrat</h1>
            </div>

            <div v-if="employeeName" class="alert alert-info mb-6">
                <i class="fas fa-info-circle mr-2"></i>
                <span>Ajout d'un contrat pour : <strong>{{ employeeName }}</strong></span>
            </div>

            <form @submit.prevent="handleSubmit" class="w-full">
                <!-- Type de contrat -->
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text font-medium">Type de contrat</span>
                    </label>
                    <div class="flex gap-4">
                        <label class="flex items-center gap-2 cursor-pointer">
                            <input type="radio" v-model="formData.type" value="CDI" class="radio radio-primary" />
                            <span>CDI</span>
                        </label>
                        <label class="flex items-center gap-2 cursor-pointer">
                            <input type="radio" v-model="formData.type" value="CDD" class="radio radio-primary" />
                            <span>CDD</span>
                        </label>
                    </div>
                    <div v-if="errors.type" class="text-error text-sm mt-1">{{ errors.type }}</div>
                </div>

                <!-- Date de début -->
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text font-medium">Date de début</span>
                    </label>
                    <input type="date" v-model="formData.startDate" class="input input-bordered w-full" />
                    <div v-if="errors.startDate" class="text-error text-sm mt-1">{{ errors.startDate }}</div>
                </div>

                <!-- Date de fin (uniquement pour CDD) -->
                <div v-if="formData.type === 'CDD'" class="form-control mb-4">
                    <label class="label">
                        <span class="label-text font-medium">Date de fin</span>
                    </label>
                    <input type="date" v-model="formData.endDate" class="input input-bordered w-full" />
                    <div v-if="errors.endDate" class="text-error text-sm mt-1">{{ errors.endDate }}</div>
                </div>

                <!-- Salaire -->
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text font-medium">Salaire (DH)</span>
                    </label>
                    <input type="number" v-model="formData.salary" class="input input-bordered w-full"
                        placeholder="Entrez le salaire" />
                    <div v-if="errors.salary" class="text-error text-sm mt-1">{{ errors.salary }}</div>
                </div>

                <!-- Poste -->
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text font-medium">Poste</span>
                    </label>
                    <input type="text" v-model="formData.position" class="input input-bordered w-full"
                        placeholder="Entrez le poste" />
                    <div v-if="errors.position" class="text-error text-sm mt-1">{{ errors.position }}</div>
                </div>

                <!-- Document du contrat -->
                <div class="form-control mb-6">
                    <label class="label">
                        <span class="label-text font-medium">Document du contrat</span>
                    </label>
                    <div class="flex flex-col gap-2">
                        <div class="border border-dashed border-gray-300 rounded-lg p-4 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
                            @click="$refs.fileInput.click()" @dragover.prevent="isDragging = true"
                            @dragleave.prevent="isDragging = false" @drop.prevent="handleFileDrop"
                            :class="{ 'bg-primary/10 border-primary': isDragging }">
                            <div class="flex flex-col items-center justify-center py-4">
                                <i class="fas fa-file-upload text-3xl mb-2"
                                    :class="selectedFile ? 'text-primary' : 'text-gray-400'"></i>
                                <p class="text-sm text-center" v-if="!selectedFile">
                                    Glissez et déposez votre fichier ici ou <span class="text-primary">parcourir</span>
                                </p>
                                <p class="text-sm text-primary font-medium" v-else>
                                    {{ selectedFile.name }}
                                </p>
                                <p class="text-xs text-gray-500 mt-1">
                                    Formats acceptés: PDF, DOC, DOCX
                                </p>
                            </div>
                        </div>
                        <input ref="fileInput" type="file" @change="handleFileChange" accept=".pdf,.doc,.docx"
                            class="hidden" />
                        <div v-if="errors.contractFile" class="text-error text-sm mt-1">{{ errors.contractFile }}</div>
                    </div>
                </div>

                <!-- Bouton de soumission -->
                <button type="submit" class="btn btn-primary w-full" :disabled="isSubmitting">
                    <i v-if="isSubmitting" class="fas fa-spinner fa-spin mr-2"></i>
                    <span>{{ isSubmitting ? 'Ajout en cours...' : 'Ajouter le contrat' }}</span>
                </button>
            </form>
        </div>
    </div>
</template>

<script>
import { useContractStore } from '@/stores/contractStore';
import { push } from 'notivue';

export default {
    name: 'AddContractView',
    data() {
        return {
            employeeId: this.$route.params.employeeId,
            employeeName: this.$route.query.employeeName || '',
            formData: {
                type: 'CDI',
                startDate: '',
                endDate: '',
                salary: '',
                position: '',
            },
            selectedFile: null,
            errors: {},
            isSubmitting: false,
            isDragging: false,
            contractStore: useContractStore()
        };
    },
    methods: {
        handleFileChange(event) {
            const file = event.target.files[0];
            if (file) {
                this.selectedFile = file;
                this.errors.contractFile = '';
            }
        },
        handleFileDrop(event) {
            this.isDragging = false;
            const file = event.dataTransfer.files[0];
            if (file && (file.type === 'application/pdf' ||
                file.type === 'application/msword' ||
                file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')) {
                this.selectedFile = file;
                this.errors.contractFile = '';
            } else {
                this.errors.contractFile = 'Format de fichier non supporté. Utilisez PDF, DOC ou DOCX.';
            }
        },
        validateForm() {
            this.errors = {};
            let isValid = true;

            if (!this.formData.type) {
                this.errors.type = 'Le type de contrat est requis';
                isValid = false;
            }

            if (!this.formData.startDate) {
                this.errors.startDate = 'La date de début est requise';
                isValid = false;
            }

            if (this.formData.type === 'CDD' && !this.formData.endDate) {
                this.errors.endDate = 'La date de fin est requise pour un CDD';
                isValid = false;
            }

            if (!this.formData.salary) {
                this.errors.salary = 'Le salaire est requis';
                isValid = false;
            }

            if (!this.formData.position) {
                this.errors.position = 'Le poste est requis';
                isValid = false;
            }

            if (!this.selectedFile) {
                this.errors.contractFile = 'Le document du contrat est requis';
                isValid = false;
            }

            return isValid;
        },
        async handleSubmit() {
            if (!this.validateForm()) {
                return;
            }

            this.isSubmitting = true;

            try {
                // Créer un FormData pour l'upload de fichier
                const contractData = new FormData();

                // Ajouter les données du formulaire
                Object.keys(this.formData).forEach(key => {
                    if (this.formData[key]) {
                        contractData.append(key, this.formData[key]);
                    }
                });

                // Ajouter l'ID de l'employé
                contractData.append('employeeId', this.employeeId);

                // Ajouter le fichier du contrat
                if (this.selectedFile) {
                    contractData.append('contractFile', this.selectedFile);
                }

                // Envoyer les données au store
                const response = await this.contractStore.addContract(contractData);
                console.log('Réponse après ajout du contrat:', response);

                // Recharger la liste des contrats pour cet employé
                if (this.employeeId) {
                    await this.contractStore.fetchContractsByEmployeeId(this.employeeId);
                } else {
                    await this.contractStore.fetchContracts();
                }

                push.success({
                    title: 'Succès',
                    message: 'Contrat ajouté avec succès'
                });

                // Rediriger vers la page de profil de l'employé
                this.$router.push(`/employees/${this.employeeId}`);
            } catch (error) {
                push.error({
                    title: 'Erreur',
                    message: error.message || 'Erreur lors de l\'ajout du contrat'
                });
            } finally {
                this.isSubmitting = false;
            }
        }
    }
};
</script>

<style scoped>
.add-contract-container {
    padding: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.card-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: hsl(var(--p));
}
</style>
