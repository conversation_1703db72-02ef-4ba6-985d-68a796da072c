import { defineStore } from 'pinia'
import { employeeService } from '@/services/employeeService'

export const useEmployeeStore = defineStore('Employee Store', {
  state: () => ({
    employees: [],
    isLoading: false,
    error: null,
  }),
  getters: {
    allEmployees: (state) => state.employees,
    hasError: (state) => !!state.error,
  },
  actions: {
    async fetchAllEmployees() {
      this.isLoading = true
      this.error = null
      try {
        this.employees = await employeeService.getAllEmployees()
      } catch (error) {
        this.error = error.message || 'Erreur lors de la récupération des employés'
      } finally {
        this.isLoading = false
      }
    },
    async addEmployee(employeeData) {
      this.isLoading = true
      this.error = null
      try {
        const newEmployee = await employeeService.addEmployee(employeeData)
        this.employees.push(newEmployee)
        return newEmployee
      } catch (error) {
        this.error = error.message || "Erreur lors de l'ajout de l'employé"
        throw error
      } finally {
        this.isLoading = false
      }
    },
    async getEmployeeById(id) {
      this.isLoading = true
      this.error = null
      try {
        const employee = await employeeService.getEmployeeById(id)
        return employee
      } catch (error) {
        this.error = error.message || "Erreur lors de la récupération de l'employé"
        throw error
      } finally {
        this.isLoading = false
      }
    },
    async updateEmployee(id, employeeData) {
      this.isLoading = true
      this.error = null
      try {
        const updatedEmployee = await employeeService.updateEmployee(id, employeeData)
        // Met à jour l'employé dans le tableau local si besoin
        const index = this.employees.findIndex((e) => e._id === id)
        if (index !== -1) {
          this.employees[index] = updatedEmployee
        }
        return updatedEmployee
      } catch (error) {
        this.error = error.message || "Erreur lors de la mise à jour de l'employé"
        throw error
      } finally {
        this.isLoading = false
      }
    },
  },
})
