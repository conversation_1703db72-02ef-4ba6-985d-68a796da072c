const express = require('express');
const router = express.Router();

// API Documentation data
const apiDocs = {
  title: 'Backoffice EMS API Documentation',
  description: 'API documentation for the Employee Management System Backoffice',
  version: '1.0.0',
  endpoints: [
    // Auth endpoints
    {
      path: '/api/auth/login',
      method: 'POST',
      description: 'Authenticate a user and create a session',
      parameters: [
        { name: 'email', type: 'string', required: true, description: 'User email address' },
        { name: 'password', type: 'string', required: true, description: 'User password' }
      ],
      responses: [
        { code: 200, description: 'Login successful', data: { user: 'User object', message: 'Login successful' } },
        { code: 401, description: 'Invalid credentials', data: { message: 'Invalid email or password' } }
      ]
    },
    {
      path: '/api/auth/logout',
      method: 'POST',
      description: 'End the current user session',
      parameters: [],
      responses: [
        { code: 200, description: 'Logout successful', data: { message: 'Logged out successfully' } }
      ]
    },
    {
      path: '/api/auth/check-session',
      method: 'GET',
      description: 'Check if the current session is valid',
      parameters: [],
      responses: [
        { code: 200, description: 'Session is valid', data: { user: 'User object', authenticated: true } },
        { code: 401, description: 'Session is invalid or expired', data: { authenticated: false } }
      ]
    },
    
    // User endpoints
    {
      path: '/api/users',
      method: 'GET',
      description: 'Get all users',
      parameters: [],
      responses: [
        { code: 200, description: 'List of users', data: ['User objects'] }
      ]
    },
    {
      path: '/api/users/:id',
      method: 'GET',
      description: 'Get a specific user by ID',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'User ID' }
      ],
      responses: [
        { code: 200, description: 'User details', data: 'User object' },
        { code: 404, description: 'User not found', data: { message: 'User not found' } }
      ]
    },
    {
      path: '/api/users',
      method: 'POST',
      description: 'Create a new user',
      parameters: [
        { name: 'email', type: 'string', required: true, description: 'User email address' },
        { name: 'password', type: 'string', required: true, description: 'User password' },
        { name: 'firstName', type: 'string', required: true, description: 'User first name' },
        { name: 'lastName', type: 'string', required: true, description: 'User last name' },
        { name: 'isAdmin', type: 'boolean', required: false, description: 'Admin status' }
      ],
      responses: [
        { code: 201, description: 'User created', data: 'User object' },
        { code: 400, description: 'Invalid input', data: { message: 'Error message' } }
      ]
    },
    {
      path: '/api/users/:id',
      method: 'PUT',
      description: 'Update a user',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'User ID' },
        { name: 'email', type: 'string', required: false, description: 'User email address' },
        { name: 'firstName', type: 'string', required: false, description: 'User first name' },
        { name: 'lastName', type: 'string', required: false, description: 'User last name' },
        { name: 'isAdmin', type: 'boolean', required: false, description: 'Admin status' }
      ],
      responses: [
        { code: 200, description: 'User updated', data: 'Updated user object' },
        { code: 404, description: 'User not found', data: { message: 'User not found' } }
      ]
    },
    {
      path: '/api/users/:id',
      method: 'DELETE',
      description: 'Delete a user',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'User ID' }
      ],
      responses: [
        { code: 200, description: 'User deleted', data: { message: 'User deleted successfully' } },
        { code: 404, description: 'User not found', data: { message: 'User not found' } }
      ]
    },
    {
      path: '/api/users/:id/profile-image',
      method: 'POST',
      description: 'Upload a profile image for a user',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'User ID' },
        { name: 'profileImage', type: 'file', required: true, description: 'Profile image file (max 5MB)' }
      ],
      responses: [
        { code: 200, description: 'Image uploaded', data: { message: 'Profile image uploaded successfully', user: 'Updated user object' } },
        { code: 400, description: 'Invalid file', data: { message: 'Error message' } }
      ]
    },
    
    // Employee endpoints
    {
      path: '/api/employees',
      method: 'GET',
      description: 'Get all employees',
      parameters: [],
      responses: [
        { code: 200, description: 'List of employees', data: ['Employee objects'] }
      ]
    },
    {
      path: '/api/employees/:id',
      method: 'GET',
      description: 'Get a specific employee by ID',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Employee ID' }
      ],
      responses: [
        { code: 200, description: 'Employee details', data: 'Employee object' },
        { code: 404, description: 'Employee not found', data: { message: 'Employee not found' } }
      ]
    },
    {
      path: '/api/employees',
      method: 'POST',
      description: 'Create a new employee',
      parameters: [
        { name: 'firstName', type: 'string', required: true, description: 'Employee first name' },
        { name: 'lastName', type: 'string', required: true, description: 'Employee last name' },
        { name: 'gender', type: 'string', required: true, description: 'Employee gender' },
        { name: 'birthDate', type: 'date', required: true, description: 'Employee birth date' },
        { name: 'phone', type: 'string', required: true, description: 'Employee phone number' },
        { name: 'email', type: 'string', required: true, description: 'Employee email address' },
        { name: 'address', type: 'string', required: true, description: 'Employee address' },
        { name: 'department', type: 'string', required: true, description: 'Department ID' },
        { name: 'hireDate', type: 'date', required: true, description: 'Employee hire date' },
        { name: 'status', type: 'string', required: true, description: 'Employee status' }
      ],
      responses: [
        { code: 201, description: 'Employee created', data: 'Employee object' },
        { code: 400, description: 'Invalid input', data: { message: 'Error message' } }
      ]
    },
    {
      path: '/api/employees/:id',
      method: 'PUT',
      description: 'Update an employee',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Employee ID' },
        { name: 'firstName', type: 'string', required: false, description: 'Employee first name' },
        { name: 'lastName', type: 'string', required: false, description: 'Employee last name' },
        { name: 'gender', type: 'string', required: false, description: 'Employee gender' },
        { name: 'birthDate', type: 'date', required: false, description: 'Employee birth date' },
        { name: 'phone', type: 'string', required: false, description: 'Employee phone number' },
        { name: 'email', type: 'string', required: false, description: 'Employee email address' },
        { name: 'address', type: 'string', required: false, description: 'Employee address' },
        { name: 'department', type: 'string', required: false, description: 'Department ID' },
        { name: 'hireDate', type: 'date', required: false, description: 'Employee hire date' },
        { name: 'status', type: 'string', required: false, description: 'Employee status' }
      ],
      responses: [
        { code: 200, description: 'Employee updated', data: 'Updated employee object' },
        { code: 404, description: 'Employee not found', data: { message: 'Employee not found' } }
      ]
    },
    {
      path: '/api/employees/:id',
      method: 'DELETE',
      description: 'Delete an employee',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Employee ID' }
      ],
      responses: [
        { code: 200, description: 'Employee deleted', data: { message: 'Employee deleted successfully' } },
        { code: 404, description: 'Employee not found', data: { message: 'Employee not found' } }
      ]
    },
    
    // Department endpoints
    {
      path: '/api/departments',
      method: 'GET',
      description: 'Get all departments',
      parameters: [],
      responses: [
        { code: 200, description: 'List of departments', data: ['Department objects'] }
      ]
    },
    {
      path: '/api/departments/:id',
      method: 'GET',
      description: 'Get a specific department by ID',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Department ID' }
      ],
      responses: [
        { code: 200, description: 'Department details', data: 'Department object' },
        { code: 404, description: 'Department not found', data: { message: 'Department not found' } }
      ]
    },
    {
      path: '/api/departments',
      method: 'POST',
      description: 'Create a new department',
      parameters: [
        { name: 'name', type: 'string', required: true, description: 'Department name' },
        { name: 'description', type: 'string', required: true, description: 'Department description' },
        { name: 'managerId', type: 'string', required: false, description: 'Manager employee ID' }
      ],
      responses: [
        { code: 201, description: 'Department created', data: 'Department object' },
        { code: 400, description: 'Invalid input', data: { message: 'Error message' } }
      ]
    },
    {
      path: '/api/departments/:id',
      method: 'PUT',
      description: 'Update a department',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Department ID' },
        { name: 'name', type: 'string', required: false, description: 'Department name' },
        { name: 'description', type: 'string', required: false, description: 'Department description' },
        { name: 'managerId', type: 'string', required: false, description: 'Manager employee ID' }
      ],
      responses: [
        { code: 200, description: 'Department updated', data: 'Updated department object' },
        { code: 404, description: 'Department not found', data: { message: 'Department not found' } }
      ]
    },
    {
      path: '/api/departments/:id',
      method: 'DELETE',
      description: 'Delete a department',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Department ID' }
      ],
      responses: [
        { code: 200, description: 'Department deleted', data: { message: 'Department deleted successfully' } },
        { code: 404, description: 'Department not found', data: { message: 'Department not found' } }
      ]
    },
    
    // Contract endpoints
    {
      path: '/api/contracts',
      method: 'GET',
      description: 'Get all contracts',
      parameters: [],
      responses: [
        { code: 200, description: 'List of contracts', data: ['Contract objects'] }
      ]
    },
    {
      path: '/api/contracts/:id',
      method: 'GET',
      description: 'Get a specific contract by ID',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Contract ID' }
      ],
      responses: [
        { code: 200, description: 'Contract details', data: 'Contract object' },
        { code: 404, description: 'Contract not found', data: { message: 'Contract not found' } }
      ]
    },
    {
      path: '/api/contracts',
      method: 'POST',
      description: 'Create a new contract',
      parameters: [
        { name: 'employeeId', type: 'string', required: true, description: 'Employee ID' },
        { name: 'fileUrl', type: 'string', required: true, description: 'Contract file URL' },
        { name: 'fileName', type: 'object', required: true, description: 'Contract file name' },
        { name: 'expiresAt', type: 'date', required: true, description: 'Contract expiration date' },
        { name: 'salary', type: 'number', required: true, description: 'Employee salary' }
      ],
      responses: [
        { code: 201, description: 'Contract created', data: 'Contract object' },
        { code: 400, description: 'Invalid input', data: { message: 'Error message' } }
      ]
    },
    {
      path: '/api/contracts/:id',
      method: 'PUT',
      description: 'Update a contract',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Contract ID' },
        { name: 'employeeId', type: 'string', required: false, description: 'Employee ID' },
        { name: 'fileUrl', type: 'string', required: false, description: 'Contract file URL' },
        { name: 'fileName', type: 'object', required: false, description: 'Contract file name' },
        { name: 'expiresAt', type: 'date', required: false, description: 'Contract expiration date' },
        { name: 'salary', type: 'number', required: false, description: 'Employee salary' }
      ],
      responses: [
        { code: 200, description: 'Contract updated', data: 'Updated contract object' },
        { code: 404, description: 'Contract not found', data: { message: 'Contract not found' } }
      ]
    },
    {
      path: '/api/contracts/:id',
      method: 'DELETE',
      description: 'Delete a contract',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Contract ID' }
      ],
      responses: [
        { code: 200, description: 'Contract deleted', data: { message: 'Contract deleted successfully' } },
        { code: 404, description: 'Contract not found', data: { message: 'Contract not found' } }
      ]
    },
    
    // Demand endpoints
    {
      path: '/api/demands',
      method: 'GET',
      description: 'Get all demands',
      parameters: [],
      responses: [
        { code: 200, description: 'List of demands', data: ['Demand objects'] }
      ]
    },
    {
      path: '/api/demands/:id',
      method: 'GET',
      description: 'Get a specific demand by ID',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Demand ID' }
      ],
      responses: [
        { code: 200, description: 'Demand details', data: 'Demand object' },
        { code: 404, description: 'Demand not found', data: { message: 'Demand not found' } }
      ]
    },
    {
      path: '/api/demands',
      method: 'POST',
      description: 'Create a new demand',
      parameters: [
        { name: 'employeeId', type: 'string', required: true, description: 'Employee ID' },
        { name: 'type', type: 'string', required: true, description: 'Demand type (e.g., vacation, sick leave)' },
        { name: 'startDate', type: 'date', required: true, description: 'Start date' },
        { name: 'endDate', type: 'date', required: true, description: 'End date' },
        { name: 'status', type: 'string', required: true, description: 'Demand status' }
      ],
      responses: [
        { code: 201, description: 'Demand created', data: 'Demand object' },
        { code: 400, description: 'Invalid input', data: { message: 'Error message' } }
      ]
    },
    {
      path: '/api/demands/:id',
      method: 'PUT',
      description: 'Update a demand',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Demand ID' },
        { name: 'employeeId', type: 'string', required: false, description: 'Employee ID' },
        { name: 'type', type: 'string', required: false, description: 'Demand type' },
        { name: 'startDate', type: 'date', required: false, description: 'Start date' },
        { name: 'endDate', type: 'date', required: false, description: 'End date' },
        { name: 'status', type: 'string', required: false, description: 'Demand status' }
      ],
      responses: [
        { code: 200, description: 'Demand updated', data: 'Updated demand object' },
        { code: 404, description: 'Demand not found', data: { message: 'Demand not found' } }
      ]
    },
    {
      path: '/api/demands/:id',
      method: 'DELETE',
      description: 'Delete a demand',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Demand ID' }
      ],
      responses: [
        { code: 200, description: 'Demand deleted', data: { message: 'Demand deleted successfully' } },
        { code: 404, description: 'Demand not found', data: { message: 'Demand not found' } }
      ]
    },
    
    // Attendance endpoints
    {
      path: '/api/attendances',
      method: 'GET',
      description: 'Get all attendance records',
      parameters: [],
      responses: [
        { code: 200, description: 'List of attendance records', data: ['Attendance objects'] }
      ]
    },
    {
      path: '/api/attendances/:id',
      method: 'GET',
      description: 'Get a specific attendance record by ID',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Attendance ID' }
      ],
      responses: [
        { code: 200, description: 'Attendance details', data: 'Attendance object' },
        { code: 404, description: 'Attendance not found', data: { message: 'Attendance not found' } }
      ]
    },
    {
      path: '/api/attendances',
      method: 'POST',
      description: 'Create a new attendance record',
      parameters: [
        { name: 'employeeId', type: 'string', required: true, description: 'Employee ID' },
        { name: 'date', type: 'date', required: true, description: 'Attendance date' },
        { name: 'checkIn', type: 'date', required: true, description: 'Check-in time' },
        { name: 'checkOut', type: 'date', required: false, description: 'Check-out time' },
        { name: 'status', type: 'string', required: true, description: 'Attendance status' }
      ],
      responses: [
        { code: 201, description: 'Attendance created', data: 'Attendance object' },
        { code: 400, description: 'Invalid input', data: { message: 'Error message' } }
      ]
    },
    {
      path: '/api/attendances/:id',
      method: 'PUT',
      description: 'Update an attendance record',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Attendance ID' },
        { name: 'employeeId', type: 'string', required: false, description: 'Employee ID' },
        { name: 'date', type: 'date', required: false, description: 'Attendance date' },
        { name: 'checkIn', type: 'date', required: false, description: 'Check-in time' },
        { name: 'checkOut', type: 'date', required: false, description: 'Check-out time' },
        { name: 'status', type: 'string', required: false, description: 'Attendance status' }
      ],
      responses: [
        { code: 200, description: 'Attendance updated', data: 'Updated attendance object' },
        { code: 404, description: 'Attendance not found', data: { message: 'Attendance not found' } }
      ]
    },
    {
      path: '/api/attendances/:id',
      method: 'DELETE',
      description: 'Delete an attendance record',
      parameters: [
        { name: 'id', type: 'string', required: true, description: 'Attendance ID' }
      ],
      responses: [
        { code: 200, description: 'Attendance deleted', data: { message: 'Attendance deleted successfully' } },
        { code: 404, description: 'Attendance not found', data: { message: 'Attendance not found' } }
      ]
    }
  ]
};

// Route to render API documentation
router.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${apiDocs.title}</title>
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
      <style>
        body {
          padding-top: 2rem;
          padding-bottom: 2rem;
          background-color: #f8f9fa;
        }
        .endpoint {
          margin-bottom: 2rem;
          border: 1px solid #dee2e6;
          border-radius: 0.25rem;
          background-color: white;
          box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .endpoint-header {
          padding: 1rem;
          border-bottom: 1px solid #dee2e6;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .endpoint-body {
          padding: 1rem;
        }
        .method {
          font-weight: bold;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          color: white;
          margin-right: 1rem;
          min-width: 80px;
          text-align: center;
        }
        .method-get {
          background-color: #0d6efd;
        }
        .method-post {
          background-color: #198754;
        }
        .method-put {
          background-color: #fd7e14;
        }
        .method-delete {
          background-color: #dc3545;
        }
        .path {
          font-family: monospace;
          font-size: 1.1rem;
        }
        .parameter-table, .response-table {
          margin-top: 1rem;
        }
        .section-title {
          margin-top: 1rem;
          margin-bottom: 0.5rem;
          font-weight: 600;
        }
        .navbar {
          margin-bottom: 2rem;
          background-color: #343a40;
        }
        .navbar-brand {
          color: white;
        }
        .nav-link {
          color: rgba(255, 255, 255, 0.75);
        }
        .nav-link:hover {
          color: white;
        }
        .category-title {
          margin-top: 3rem;
          margin-bottom: 1.5rem;
          padding-bottom: 0.5rem;
          border-bottom: 2px solid #dee2e6;
        }
        .category-title:first-of-type {
          margin-top: 0;
        }
        pre {
          background-color: #f8f9fa;
          padding: 1rem;
          border-radius: 0.25rem;
          overflow: auto;
        }
        code {
          font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        }
        .response-code {
          font-weight: bold;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          margin-right: 0.5rem;
        }
        .response-200 {
          background-color: #d1e7dd;
          color: #0f5132;
        }
        .response-201 {
          background-color: #d1e7dd;
          color: #0f5132;
        }
        .response-400 {
          background-color: #f8d7da;
          color: #842029;
        }
        .response-401 {
          background-color: #f8d7da;
          color: #842029;
        }
        .response-404 {
          background-color: #f8d7da;
          color: #842029;
        }
        .toc {
          position: sticky;
          top: 2rem;
          height: calc(100vh - 4rem);
          overflow-y: auto;
          padding-right: 1rem;
        }
        .toc-link {
          display: block;
          padding: 0.25rem 0;
          color: #6c757d;
          text-decoration: none;
        }
        .toc-link:hover {
          color: #0d6efd;
        }
        .toc-section {
          font-weight: bold;
          margin-top: 1rem;
          color: #343a40;
        }
        .toc-section:first-child {
          margin-top: 0;
        }
        @media (max-width: 992px) {
          .toc {
            position: static;
            height: auto;
            margin-bottom: 2rem;
          }
        }
      </style>
    </head>
    <body>
      <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
          <a class="navbar-brand" href="#">${apiDocs.title}</a>
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
              <li class="nav-item">
                <a class="nav-link" href="#overview">Overview</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#authentication">Authentication</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#endpoints">Endpoints</a>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      <div class="container">
        <div class="row">
          <div class="col-lg-3">
            <div class="toc">
              <div class="toc-section">Overview</div>
              <a class="toc-link" href="#overview">Introduction</a>
              <a class="toc-link" href="#authentication">Authentication</a>
              
              <div class="toc-section">Endpoints</div>
              <a class="toc-link" href="#auth-endpoints">Authentication</a>
              <a class="toc-link" href="#user-endpoints">Users</a>
              <a class="toc-link" href="#employee-endpoints">Employees</a>
              <a class="toc-link" href="#department-endpoints">Departments</a>
              <a class="toc-link" href="#contract-endpoints">Contracts</a>
              <a class="toc-link" href="#demand-endpoints">Demands</a>
              <a class="toc-link" href="#attendance-endpoints">Attendance</a>
            </div>
          </div>
          
          <div class="col-lg-9">
            <section id="overview">
              <h1>${apiDocs.title}</h1>
              <p class="lead">${apiDocs.description}</p>
              <p>Version: ${apiDocs.version}</p>
              
              <div class="card mb-4">
                <div class="card-body">
                  <h5 class="card-title">Base URL</h5>
                  <pre><code>http://localhost:${process.env.PORT || 3000}/api</code></pre>
                </div>
              </div>
            </section>
            
            <section id="authentication">
              <h2>Authentication</h2>
              <p>This API uses session-based authentication. You must first log in using the <code>/api/auth/login</code> endpoint to obtain a session cookie. All subsequent requests should include this cookie.</p>
              <p>Most endpoints require authentication. Unauthenticated requests will receive a 401 Unauthorized response.</p>
            </section>
            
            <section id="endpoints">
              <h2>API Endpoints</h2>
              
              <h3 id="auth-endpoints" class="category-title">Authentication Endpoints</h3>
              ${renderEndpoints(apiDocs.endpoints.filter(e => e.path.startsWith('/api/auth')))}
              
              <h3 id="user-endpoints" class="category-title">User Endpoints</h3>
              ${renderEndpoints(apiDocs.endpoints.filter(e => e.path.startsWith('/api/users')))}
              
              <h3 id="employee-endpoints" class="category-title">Employee Endpoints</h3>
              ${renderEndpoints(apiDocs.endpoints.filter(e => e.path.startsWith('/api/employees')))}
              
              <h3 id="department-endpoints" class="category-title">Department Endpoints</h3>
              ${renderEndpoints(apiDocs.endpoints.filter(e => e.path.startsWith('/api/departments')))}
              
              <h3 id="contract-endpoints" class="category-title">Contract Endpoints</h3>
              ${renderEndpoints(apiDocs.endpoints.filter(e => e.path.startsWith('/api/contracts')))}
              
              <h3 id="demand-endpoints" class="category-title">Demand Endpoints</h3>
              ${renderEndpoints(apiDocs.endpoints.filter(e => e.path.startsWith('/api/demands')))}
              
              <h3 id="attendance-endpoints" class="category-title">Attendance Endpoints</h3>
              ${renderEndpoints(apiDocs.endpoints.filter(e => e.path.startsWith('/api/attendances')))}
            </section>
          </div>
        </div>
      </div>
      
      <footer class="container mt-5 pt-5 text-muted border-top">
        <p>&copy; 2023 Backoffice EMS</p>
      </footer>
      
      <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
  `);
});

// Helper function to render endpoints
function renderEndpoints(endpoints) {
  return endpoints.map(endpoint => `
    <div class="endpoint">
      <div class="endpoint-header">
        <div class="d-flex align-items-center">
          <span class="method method-${endpoint.method.toLowerCase()}">${endpoint.method}</span>
          <span class="path">${endpoint.path}</span>
        </div>
      </div>
      <div class="endpoint-body">
        <p>${endpoint.description}</p>
        
        ${endpoint.parameters.length > 0 ? `
          <div class="section-title">Parameters</div>
          <table class="table table-bordered parameter-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              ${endpoint.parameters.map(param => `
                <tr>
                  <td><code>${param.name}</code></td>
                  <td>${param.type}</td>
                  <td>${param.required ? 'Yes' : 'No'}</td>
                  <td>${param.description}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        ` : '<p>No parameters required</p>'}
        
        <div class="section-title">Responses</div>
        <table class="table table-bordered response-table">
          <thead>
            <tr>
              <th>Status</th>
              <th>Description</th>
              <th>Data</th>
            </tr>
          </thead>
          <tbody>
            ${endpoint.responses.map(response => `
              <tr>
                <td><span class="response-code response-${response.code}">${response.code}</span></td>
                <td>${response.description}</td>
                <td><pre><code>${JSON.stringify(response.data, null, 2)}</code></pre></td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    </div>
  `).join('');
}

module.exports = router;