<template>
  <div class="overflow-x-auto rounded-xl shadow bg-base-100 p-6">
    <table class="table table-zebra w-full">
      <thead>
        <tr>
          <th v-for="header in headers" :key="header.value" class="font-bold uppercase">
            {{ header.label }}
          </th>
          <th v-if="showActions" class="font-bold uppercase">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(row, idx) in data" :key="row._id || idx" class="hover:bg-base-200 cursor-pointer">
          <td v-for="header in headers" :key="header.value" class="py-2 px-4">
            <slot :name="`cell-${header.value}`" :row="row">
              {{ row[header.value] }}
            </slot>
          </td>
          <td v-if="showActions" class="py-2 px-4">
            <slot name="actions" :row="row">
              <button class="btn btn-sm btn-outline btn-info mr-2" @click="$emit('onEdit', row._id)">Modifier</button>
              <button class="btn btn-sm btn-outline btn-error" @click="$emit('onDelete', row._id)">Supprimer</button>
            </slot>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: 'DataTable',
  props: {
    headers: {
      type: Array,
      required: true
    },
    data: {
      type: Array,
      required: true
    },
    showActions: {
      type: Boolean,
      default: false
    }
  }
};
</script>
