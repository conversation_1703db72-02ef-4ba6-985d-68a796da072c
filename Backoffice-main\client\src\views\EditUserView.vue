<template>
    <div class="edit-user-container">
        <h1>Modifier l'utilisateur</h1>
        <FormBase v-if="user" :fields="formFields" :validation-rules="validationRules" :initial-values="form"
            :profile-image="profileImageWithCacheBuster" buttonText="Enregistrer" @onSubmit="handleSubmit" />
        <div v-else>
            Chargement des données de l'utilisateur...
        </div>
    </div>
</template>

<script>
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/stores/userStore';
import { push } from 'notivue';
import FormBase from '@/components/Form.vue';

export default {
    name: 'EditUserView',
    components: { FormBase },
    data() {
        return {
            user: null,
            form: {
                firstName: '',
                lastName: '',
                email: '',
                isAdmin: false,
                profileImage: ''
            },
            formFields: [
                { name: 'firstName', label: 'Prénom', type: 'text', placeholder: 'Entrez le prénom' },
                { name: 'lastName', label: 'Nom', type: 'text', placeholder: 'Entrez le nom' },
                { name: 'email', label: 'Email', type: 'email', placeholder: 'Entrez l\'email' },
                {
                    name: 'isAdmin', label: 'Rôle', type: 'radio', options: [
                        { value: true, label: 'Administrateur' },
                        { value: false, label: 'Gestionnaire' }
                    ]
                },
                { name: 'avatar', label: 'Avatar', type: 'file' }
            ],
            validationRules: {
                firstName: [{ required: true, message: 'Le prénom est requis' }],
                lastName: [{ required: true, message: 'Le nom est requis' }],
                email: [
                    { required: true, message: 'L\'email est requis' },
                    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: 'Veuillez entrer une adresse email valide' }
                ],
                isAdmin: [{ required: true, message: 'Le rôle est requis' }]
            },
            cacheBuster: Date.now()
        };
    },
    computed: {
        profileImageWithCacheBuster() {
            return this.form.profileImage ? this.form.profileImage + '?v=' + this.cacheBuster : '';
        }
    },
    created() {
        this.loadUser();
    },
    methods: {
        async loadUser() {
            try {
                const route = useRoute();
                const userStore = useUserStore();
                const userId = route.params.id;
                this.user = await userStore.getUserById(userId);
                if (this.user) {
                    this.form.firstName = this.user.firstName;
                    this.form.lastName = this.user.lastName;
                    this.form.email = this.user.email;
                    this.form.isAdmin = this.user.isAdmin;
                    this.form.profileImage = this.user.profileImage || '';
                }
            } catch (error) {
                push.error({ title: 'Erreur', message: "Impossible de charger l'utilisateur" });
            }
        },
        async handleSubmit(formData) {
            try {
                const userStore = useUserStore();
                let userData;
                if (formData.avatar) {
                    userData = new FormData();
                    userData.append('firstName', formData.firstName);
                    userData.append('lastName', formData.lastName);
                    userData.append('email', formData.email);
                    userData.append('isAdmin', formData.isAdmin);
                    userData.append('profileImage', formData.avatar);
                } else {
                    userData = {
                        firstName: formData.firstName,
                        lastName: formData.lastName,
                        email: formData.email,
                        isAdmin: formData.isAdmin
                    };
                }
                await userStore.updateUser(this.user._id, userData);
                // Recharge les données utilisateur pour mettre à jour l’image affichée
                const updatedUser = await userStore.getUserById(this.user._id);
                this.form.profileImage = updatedUser.profileImage || '';
                this.cacheBuster = Date.now(); // Force le rafraîchissement de l'image
                push.success({ title: 'Succès', message: 'Utilisateur modifié avec succès' });
                this.$router.push('/users');
            } catch (error) {
                push.error({ title: 'Erreur', message: "Erreur lors de la modification" });
            }
        }
    }
};
</script>

<style scoped>
.edit-user-container {
    max-width: 500px;
    margin: 2rem auto;
    background: #fff;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
}

.edit-user-container h1 {
    text-align: center;
    margin-bottom: 2rem;
}

.edit-user-container form>div {
    margin-bottom: 1.2rem;
}

.edit-user-container label {
    display: block;
    margin-bottom: 0.3rem;
    font-weight: 500;
}

.edit-user-container input,
.edit-user-container select {
    width: 100%;
    padding: 0.5rem;
    border-radius: 0.3rem;
    border: 1px solid #ddd;
}
</style>