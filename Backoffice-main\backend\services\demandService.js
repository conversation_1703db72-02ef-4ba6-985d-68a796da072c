const Demand = require("../models/Demand");

const createDemand = async (data) => {
  const demand = new Demand(data);
  return await demand.save();
};

const getAllDemands = async (filter = {}) => {
  return await Demand.find(filter);
};

const getDemandById = async (id) => {
  return await Demand.findById(id).populate("employee").populate("createdBy");
};

const updateDemand = async (id, data) => {
  return await Demand.findByIdAndUpdate(id, data, { new: true });
};

const deleteDemand = async (id) => {
  return await Demand.findByIdAndDelete(id);
};

const getDemandsByEmployeeId = async (employeeId) => {
  try {
    console.log("Service - Recherche des demandes pour l'employé:", employeeId);
    // Rechercher par le champ employee (ObjectId) ou employeeId (String)
    const demands = await Demand.find({
      $or: [{ employee: employeeId }, { employeeId: employeeId }],
    }).sort({ createdAt: -1 });
    console.log("Service - Demandes trouvées:", demands);
    return demands;
  } catch (error) {
    console.error(
      "Service - Erreur lors de la récupération des demandes par employé:",
      error
    );
    throw error;
  }
};

module.exports = {
  createDemand,
  getAllDemands,
  getDemandById,
  updateDemand,
  deleteDemand,
  getDemandsByEmployeeId,
};
