<template>
  <div class="add-employee-container flex justify-center items-center min-h-[60vh]">
    <div class="card w-full max-w-xl p-6 md:p-10 bg-base-100 shadow-xl rounded-xl">
      <h1 class="card-title text-center mb-6">Ajouter un employé</h1>

      <div class="profile-image-upload">
        <div class="image-preview" @click="triggerFileInput">
          <img v-if="imagePreview" :src="imagePreview" alt="Aperçu de l'image" />
          <div v-else class="no-image">
            <i class="fas fa-user"></i>
            <span>Cliquez pour ajouter une photo</span>
          </div>
        </div>
        <input type="file" ref="fileInput" accept="image/*" @change="handleImageUpload" class="hidden-input" />
      </div>

      <FormBase :fields="formFields" :validation-rules="validationRules" buttonText="Ajouter l'employé"
        @onSubmit="handleSubmit" class="w-full" />
    </div>
  </div>
</template>

<script>
import { useEmployeeStore } from '@/stores/employeeStore';
import { push } from 'notivue';
import FormBase from '@/components/Form.vue';

export default {
  name: 'AddEmployeeView',
  components: { FormBase },
  data() {
    return {
      formFields: [
        { name: 'firstName', label: 'Prénom', type: 'text', placeholder: 'Entrez le prénom' },
        { name: 'lastName', label: 'Nom', type: 'text', placeholder: 'Entrez le nom' },
        {
          name: 'gender', label: 'Genre', type: 'radio', options: [
            { value: 'Homme', label: 'Homme' }, { value: 'Femme', label: 'Femme' }
          ]
        },
        { name: 'birthDate', label: 'Date de naissance', type: 'date', placeholder: '' },
        { name: 'phone', label: 'Téléphone', type: 'text', placeholder: 'Entrez le téléphone' },
        { name: 'email', label: 'Email', type: 'email', placeholder: 'Entrez l\'email' },
        { name: 'address', label: 'Adresse', type: 'text', placeholder: 'Entrez l\'adresse' },
        { name: 'department', label: 'Département', type: 'text', placeholder: 'Entrez le département' },
        { name: 'hireDate', label: 'Date d\'embauche', type: 'date', placeholder: '' },
        {
          name: 'status', label: 'Statut', type: 'radio', options: [
            { value: 'Actif', label: 'Actif' }, { value: 'Inactif', label: 'Inactif' }
          ]
        }
      ],
      validationRules: {
        firstName: [{ required: true, message: 'Le prénom est requis' }],
        lastName: [{ required: true, message: 'Le nom est requis' }],
        gender: [{ required: true, message: 'Le genre est requis' }],
        birthDate: [{ required: true, message: 'La date de naissance est requise' }],
        phone: [{ required: true, message: 'Le téléphone est requis' }],
        email: [
          { required: true, message: 'L\'email est requis' },
          { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: 'Veuillez entrer une adresse email valide' }
        ],
        address: [{ required: true, message: 'L\'adresse est requise' }],
        department: [{ required: true, message: 'Le département est requis' }],
        hireDate: [{ required: true, message: 'La date d\'embauche est requise' }],
        status: [{ required: true, message: 'Le statut est requis' }]
      },
      imageFile: null,
      imagePreview: null,
      employeeStore: useEmployeeStore()
    };
  },
  methods: {
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    handleImageUpload(event) {
      const file = event.target.files[0];
      if (!file) return;
      if (!file.type.match('image.*')) {
        push.error({ title: 'Erreur', message: 'Veuillez sélectionner une image valide' });
        return;
      }
      this.resizeImage(file);
    },
    resizeImage(file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new window.Image();
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const MAX_WIDTH = 300;
          const MAX_HEIGHT = 300;
          let width = img.width;
          let height = img.height;
          if (width > height) {
            if (width > MAX_WIDTH) {
              height *= MAX_WIDTH / width;
              width = MAX_WIDTH;
            }
          } else {
            if (height > MAX_HEIGHT) {
              width *= MAX_HEIGHT / height;
              height = MAX_HEIGHT;
            }
          }
          canvas.width = width;
          canvas.height = height;
          ctx.drawImage(img, 0, 0, width, height);
          canvas.toBlob((blob) => {
            const resizedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            this.imagePreview = URL.createObjectURL(resizedFile);
            this.imageFile = resizedFile;
          }, file.type);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    },
    async handleSubmit(formData) {
      try {
        let createdBy = "admin";
        if (this.$pinia && this.$pinia._s && this.$pinia._s.size > 0) {
          const authStore = this.$pinia._s.get('auth');
          if (authStore && authStore.user && authStore.user._id) {
            createdBy = authStore.user._id;
          }
        }
        let employeeData;
        if (this.imageFile) {
          employeeData = new FormData();
          Object.keys(formData).forEach(key => {
            employeeData.append(key, formData[key]);
          });
          employeeData.append('profileImage', this.imageFile);
          employeeData.append('createdBy', createdBy);
        } else {
          employeeData = { ...formData, createdBy };
        }
        await this.employeeStore.addEmployee(employeeData);
        push.success({ title: 'Succès', message: 'Employé ajouté avec succès' });
        this.$router.push('/employees');
      } catch (error) {
        push.error({ title: 'Erreur', message: error.message || 'Erreur lors de l\'ajout de l\'employé' });
      }
    }
  }
};
</script>

<style scoped>
add-employee-container {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.card {
  background-color: hsl(var(--b1));
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 2rem;
}

.card-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  text-align: center;
  color: hsl(var(--p));
}

.profile-image-upload {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.image-preview {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: hsl(var(--b2));
  cursor: pointer;
  transition: all 0.3s ease;
  border: 3px solid hsl(var(--p));
}

.image-preview:hover {
  transform: scale(1.05);
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: hsl(var(--bc));
  padding: 1rem;
  text-align: center;
}

.no-image i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.hidden-input {
  display: none;
}
</style>