<template>
  <div class="flex justify-center items-center gap-2 my-8">
    <button
      class="btn btn-sm btn-outline"
      :disabled="currentPage === 1"
      @click="$emit('update:currentPage', currentPage - 1)"
    >
      Précédent
    </button>
    <button
      v-for="page in totalPages"
      :key="page"
      class="btn btn-sm"
      :class="page === currentPage ? 'btn-primary text-white' : 'btn-ghost'"
      @click="$emit('update:currentPage', page)"
    >
      {{ page }}
    </button>
    <button
      class="btn btn-sm btn-outline"
      :disabled="currentPage === totalPages"
      @click="$emit('update:currentPage', currentPage + 1)"
    >
      Suivant
    </button>
  </div>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    currentPage: {
      type: Number,
      required: true
    },
    totalPages: {
      type: Number,
      required: true
    }
  }
};
</script>

