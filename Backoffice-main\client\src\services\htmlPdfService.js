import html_to_pdf from 'html-pdf-node'
import { formatDate } from '@/utils/dateUtils'

/**
 * Génère un template HTML pour une demande
 * @param {Object} demand - L'objet demande
 * @param {Object} employee - L'objet employé
 * @returns {String} - Le template HTML
 */
function generateDemandTemplate(demand, employee) {
  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Demande de ${demand.type}</title>
      <style>
        body {
          font-family: 'Helvetica', 'Arial', sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 1px solid #eee;
          padding-bottom: 20px;
        }
        .header h1 {
          color: #2c3e50;
          margin-bottom: 5px;
        }
        .header p {
          color: #7f8c8d;
          margin: 5px 0;
          font-size: 14px;
        }
        .section {
          margin-bottom: 25px;
        }
        .section h2 {
          color: #2c3e50;
          border-bottom: 1px solid #eee;
          padding-bottom: 5px;
          font-size: 18px;
        }
        .section-content {
          padding-left: 20px;
        }
        .field {
          margin-bottom: 10px;
        }
        .field-label {
          font-weight: bold;
          display: inline-block;
          min-width: 120px;
        }
        .footer {
          margin-top: 50px;
          text-align: center;
          font-size: 12px;
          color: #7f8c8d;
          border-top: 1px solid #eee;
          padding-top: 20px;
        }
        @media print {
          body {
            padding: 0;
          }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Demande de ${demand.type}</h1>
        <p>Référence: DEM-${demand._id?.substring(0, 8) || 'N/A'}</p>
        <p>Date d'émission: ${formatDate(new Date())}</p>
      </div>
      
      <div class="section">
        <h2>Informations de l'employé</h2>
        <div class="section-content">
          <div class="field">
            <span class="field-label">Nom:</span>
            <span>${employee.lastName}</span>
          </div>
          <div class="field">
            <span class="field-label">Prénom:</span>
            <span>${employee.firstName}</span>
          </div>
          <div class="field">
            <span class="field-label">Email:</span>
            <span>${employee.email}</span>
          </div>
          <div class="field">
            <span class="field-label">Département:</span>
            <span>${employee.department || 'Non spécifié'}</span>
          </div>
        </div>
      </div>
      
      <div class="section">
        <h2>Détails de la demande</h2>
        <div class="section-content">
          <div class="field">
            <span class="field-label">Type:</span>
            <span>${demand.type}</span>
          </div>
          <div class="field">
            <span class="field-label">Date de dépôt:</span>
            <span>${formatDate(demand.requestedAt)}</span>
          </div>
          <div class="field">
            <span class="field-label">Statut:</span>
            <span>${demand.status}</span>
          </div>
          ${
            demand.startDate
              ? `
          <div class="field">
            <span class="field-label">Date de début:</span>
            <span>${formatDate(demand.startDate)}</span>
          </div>
          `
              : ''
          }
          ${
            demand.endDate
              ? `
          <div class="field">
            <span class="field-label">Date de fin:</span>
            <span>${formatDate(demand.endDate)}</span>
          </div>
          `
              : ''
          }
        </div>
      </div>
      
      ${
        demand.reason
          ? `
      <div class="section">
        <h2>Motif de la demande</h2>
        <div class="section-content">
          <p>${demand.reason.replace(/\n/g, '<br>')}</p>
        </div>
      </div>
      `
          : ''
      }
      
      <div class="footer">
        <p>Document généré automatiquement par BackOffice EMS</p>
        <p>&copy; ${new Date().getFullYear()} BackOffice EMS. Tous droits réservés.</p>
      </div>
    </body>
    </html>
  `
}

/**
 * Génère un PDF à partir d'un template HTML
 * @param {Object} demand - L'objet demande
 * @param {Object} employee - L'objet employé
 * @returns {Promise<Buffer>} - Le buffer du PDF généré
 */
export async function generateDemandPDFFromHTML(demand, employee) {
  const htmlContent = generateDemandTemplate(demand, employee)

  const options = {
    format: 'A4',
    margin: {
      top: '20mm',
      right: '20mm',
      bottom: '20mm',
      left: '20mm',
    },
    printBackground: true,
    preferCSSPageSize: true,
  }

  const file = { content: htmlContent }

  try {
    return await html_to_pdf.generatePdf(file, options)
  } catch (error) {
    console.error('Erreur lors de la génération du PDF:', error)
    throw error
  }
}
