import { defineStore } from 'pinia';
import { departmentService } from '@/services/departmentService';

export const useDepartmentStore = defineStore('department', {
  state: () => ({
    departments: [],
    loading: false,
    error: null
  }),
  actions: {
    async fetchDepartments() {
      this.loading = true;
      try {
        this.departments = await departmentService.getDepartments();
        this.error = null;
      } catch (err) {
        this.error = err;
      } finally {
        this.loading = false;
      }
    },
    async addDepartment(department) {
      try {
        const newDept = await departmentService.addDepartment(department);
        this.departments.push(newDept);
        return newDept;
      } catch (err) {
        this.error = err;
        throw err;
      }
    },
    async updateDepartment(id, department) {
      try {
        const updatedDept = await departmentService.updateDepartment(id, department);
        const index = this.departments.findIndex(d => d._id === id);
        if (index !== -1) {
          this.departments[index] = updatedDept;
        }
        return updatedDept;
      } catch (err) {
        this.error = err;
        throw err;
      }
    },
    async deleteDepartment(id) {
      try {
        await departmentService.deleteDepartment(id);
        this.departments = this.departments.filter(d => d._id !== id);
      } catch (err) {
        this.error = err;
        throw err;
      }
    },
  }
});