const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const contractSchema = new Schema(
  {
    employeeId: {
      type: String,
      required: true,
    },
    // Ajout d'une référence à l'employé pour la compatibilité
    employee: {
      type: Schema.Types.ObjectId,
      ref: "Employee",
    },
    type: {
      type: String,
      enum: ["CDI", "CDD"],
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: function () {
        return this.type === "CDD";
      },
    },
    position: {
      type: String,
      required: true,
    },
    fileUrl: {
      type: String,
      required: true,
    },
    fileName: {
      name: String,
      url: String,
      path: String,
    },
    uploadedAt: {
      type: Date,
      default: Date.now,
    },
    expiresAt: {
      type: Date,
      required: function () {
        return this.type === "CDD";
      },
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    salary: {
      type: Number,
      required: true,
    },
  },
  { timestamps: true }
);

// Middleware pre-save pour s'assurer que employee est défini
contractSchema.pre("save", function (next) {
  if (this.employeeId && !this.employee) {
    try {
      this.employee = mongoose.Types.ObjectId(this.employeeId);
    } catch (error) {
      // Si l'employeeId n'est pas un ObjectId valide, on continue sans définir employee
      console.log(
        "Impossible de convertir employeeId en ObjectId:",
        this.employeeId
      );
    }
  }
  next();
});

module.exports = mongoose.model("Contract", contractSchema);
