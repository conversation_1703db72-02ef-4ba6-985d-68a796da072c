<template>
    <div class="attendance-container">
        <div class="header">
            <h2>Tableau des Présences de la période <span class="period">{{ selectedPeriod }}</span></h2>
            <button @click="toggleMonthPicker" class="month-btn">
                choisir le mois ▾
            </button>
            <div v-if="showMonthPicker" class="month-picker">
                <input type="month" v-model="monthInput" @change="changeMonth" />
            </div>
        </div>
        <div class="search-bar">
            <input v-model="search" placeholder="chercher par nom ou prénom de l'employé" />
            <button @click="fetchAttendance">Chercher</button>
        </div>
        <DataTable :headers="headers" :data="filteredAttendance" />
    </div>
</template>

<script>
import { useAttendanceStore } from '@/stores/attendanceStore'
import DataTable from '@/components/Table.vue'

export default {
    name: 'AttendanceView',
    components: { DataTable },
    data() {
        return {
            search: '',
            selectedPeriod: 'avr-25',
            showMonthPicker: false,
            monthInput: '2024-04',
            headers: [
                { label: 'ID', value: 'id' },
                { label: 'Nom et Prénom', value: 'name' },
                { label: 'Jours Présents', value: 'presentDays' },
                { label: 'Absence', value: 'absence' },
                { label: 'Retard', value: 'late' },
                { label: 'Heures Sup', value: 'overtime' },
                { label: 'Département', value: 'department' }
            ]
        };
    },
    computed: {
        attendanceStore() {
            return useAttendanceStore();
        },
        attendances() {
            return this.attendanceStore.attendances;
        },
        filteredAttendance() {
            if (!this.search) return this.attendances;
            return this.attendances.filter(row =>
                row.name.toLowerCase().includes(this.search.toLowerCase())
            );
        }
    },
    methods: {
        async fetchAttendance() {
            await this.attendanceStore.fetchAttendances(this.monthInput);
        },
        changeMonth() {
            const [year, month] = this.monthInput.split('-');
            const mois = ['jan', 'fév', 'mar', 'avr', 'mai', 'jui', 'jui', 'aoû', 'sep', 'oct', 'nov', 'déc'];
            this.selectedPeriod = `${mois[parseInt(month) - 1]}-${year.slice(2)}`;
            this.fetchAttendance();
        },
        toggleMonthPicker() {
            this.showMonthPicker = !this.showMonthPicker;
        }
    },
    mounted() {
        this.fetchAttendance();
    }
};
</script>

<style scoped>
.attendance-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
}

.header {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.period {
    color: #2196f3;
    font-weight: bold;
}

.month-btn {
    background: #2196f3;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    cursor: pointer;
}

.month-picker {
    margin-left: 1rem;
}

.search-bar {
    margin: 1.5rem 0;
    display: flex;
    gap: 1rem;
}

.attendance-table {
    width: 100%;
    border-collapse: collapse;
}

.attendance-table th,
.attendance-table td {
    border-bottom: 1px solid #ddd;
    padding: 0.75rem;
    text-align: center;
}

.attendance-table th {
    background: #f5f5f5;
}
</style>