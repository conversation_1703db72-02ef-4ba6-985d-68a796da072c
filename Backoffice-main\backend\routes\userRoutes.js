const express = require("express");
const router = express.Router();
const userController = require("../controllers/userController");
const authMiddleware = require("../middleware/authMiddleware");
const upload = require("../middleware/uploadMiddleware");

// Routes existantes
router.post("/", userController.createUser);
router.get("/", userController.getUsers);
router.get("/:id", userController.getUserById);
router.put("/:id", authMiddleware, userController.updateUser);
router.delete("/:id", userController.deleteUser);

// Route for uploading profile image
router.post(
  "/:id/profile-image",
  authMiddleware,
  upload.single("profileImage"),
  userController.uploadProfileImage
);

module.exports = router;
