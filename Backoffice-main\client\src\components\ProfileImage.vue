<template>
  <div class="profile-image-container">
    <div class="avatar">
      <div class="w-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
        <img :src="imageUrl" alt="Profile" @error="handleImageError" />
      </div>
    </div>

    <div v-if="editable" class="mt-4">
      <label class="btn btn-outline btn-primary">
        <input type="file" accept="image/*" class="hidden" @change="handleFileChange" />
        <span>Charger la Photo</span>
      </label>
    </div>
  </div>
</template>

<script>
import { userService } from '@/services/userService';
import { push } from 'notivue';

export default {
  name: 'ProfileImage',
  props: {
    userId: {
      type: String,
      required: true
    },
    profileImage: {
      type: String,
      default: ''
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:profileImage'],
  data() {
    return {
      isUploading: false
    };
  },
  computed: {
    baseUrl() {
      return import.meta.env.VITE_API_URL || '';
    },
    imageUrl() {
      if (!this.profileImage) {
        // Générer un avatar avec l'ID utilisateur comme seed
        return `https://ui-avatars.com/api/?name=${this.userId}&background=random`;
      }
      // Si le chemin de l'image est une URL complète, l'utiliser directement
      if (this.profileImage.startsWith('http')) {
        return this.profileImage;
      }
      // Sinon, préfixer avec l'URL de l'API
      return `${this.baseUrl}${this.profileImage}`;
    }
  },
  methods: {
    handleImageError(e) {
      // Repli sur avatar généré en cas d'erreur
      e.target.src = `https://ui-avatars.com/api/?name=${this.userId}&background=random`;
    },
    async handleFileChange(e) {
      const file = e.target.files[0];
      if (!file) return;

      // Vérifier le type de fichier
      if (!file.type.startsWith('image/')) {
        push.error({
          title: 'Erreur',
          message: 'Veuillez sélectionner un fichier image',
          type: 'error'
        });
        return;
      }

      // Vérifier la taille du fichier (max 5Mo)
      if (file.size > 5 * 1024 * 1024) {
        push.error({
          title: 'Erreur',
          message: 'La taille de l\'image doit être inférieure à 5Mo',
          type: 'error'
        });
        return;
      }

      try {
        this.isUploading = true;

        // Créer le form data
        const formData = new FormData();
        formData.append('profileImage', file);

        // Envoyer l'image
        const response = await userService.uploadProfileImage(this.userId, formData);

        // Mettre à jour l'image de profil
        this.$emit('update:profileImage', response.user.profileImage + '?v=' + Date.now());

        push.success({
          title: 'Succès',
          message: 'Image de profil mise à jour avec succès',
          type: 'success'
        });
      } catch (error) {
        console.error('Erreur lors de l\'upload de l\'image :', error);
        push.error({
          title: 'Erreur',
          message: error.message || 'Échec de l\'upload de l\'image',
          type: 'error'
        });
      } finally {
        this.isUploading = false;
      }
    }
  }
};
</script>

<style scoped>
.profile-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

input[type="file"] {
  display: none;
}
</style>