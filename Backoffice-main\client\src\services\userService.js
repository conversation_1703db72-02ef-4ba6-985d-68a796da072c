import api from '@/api'

export const userService = {
  async login(email, password) {
    try {
      const response = await api.post('/auth/login', { email, password }, { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Login failed')
    }
  },

  async logout() {
    try {
      const response = await api.post('/auth/logout', {}, { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Logout failed')
    }
  },

  async checkSession() {
    try {
      const response = await api.get('/auth/check-session', { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Session check failed')
    }
  },

  async getAllUsers() {
    try {
      const response = await api.get('/users', { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users')
    }
  },

  async getUserById(id) {
    try {
      const response = await api.get(`/users/${id}`, { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch user')
    }
  },

  async addUser(userData) {
    try {
      const response = await api.post('/users', userData, { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to create user')
    }
  },

  async updateUser(id, userData) {
    try {
      const response = await api.put(`/users/${id}`, userData, { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update user')
    }
  },

  async deleteUser(id) {
    try {
      const response = await api.delete(`/users/${id}`, { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete user')
    }
  },

  async uploadProfileImage(userId, formData) {
    try {
      const response = await api.post(`/users/${userId}/profile-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        withCredentials: true,
      })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to upload profile image')
    }
  }
}
