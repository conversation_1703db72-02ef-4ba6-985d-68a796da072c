import { defineStore } from 'pinia'
import { demandService } from '@/services/demandService'

export const useDemandStore = defineStore('demandStore', {
  state: () => ({
    demands: [],
    pendingDemands: [],
    isLoading: false,
    error: null,
  }),

  getters: {
    // Getter pour obtenir le nombre de demandes en attente
    pendingDemandsCount: (state) => state.pendingDemands.length,
  },

  actions: {
    async fetchDemands() {
      this.isLoading = true
      this.error = null
      try {
        const demands = await demandService.getAllDemands()
        this.demands = demands
        console.log('Demandes récupérées dans le store:', demands)

        // Filtrer les demandes en attente
        this.pendingDemands = demands.filter((d) => d.status === 'EN_ATTENTE')
        console.log('Demandes en attente filtrées:', this.pendingDemands)

        return demands
      } catch (err) {
        this.error = err.message || 'Erreur lors du chargement des demandes'
        console.error('Erreur dans fetchDemands:', err)
        return []
      } finally {
        this.isLoading = false
      }
    },

    // Nouvelle action pour récupérer uniquement les demandes en attente
    async fetchPendingDemands() {
      this.isLoading = true
      this.error = null
      try {
        const pendingDemands = await demandService.getPendingDemands()
        this.pendingDemands = pendingDemands
        console.log('Demandes en attente récupérées:', pendingDemands)
        return pendingDemands
      } catch (err) {
        this.error = err.message || 'Erreur lors du chargement des demandes en attente'
        console.error('Erreur dans fetchPendingDemands:', err)
        this.pendingDemands = []
        return []
      } finally {
        this.isLoading = false
      }
    },

    async addDemand(demandData) {
      try {
        const newDemand = await demandService.addDemand(demandData)
        this.demands.push(newDemand)
        return newDemand
      } catch (err) {
        this.error = err.message || "Erreur lors de l'ajout de la demande"
        throw err
      }
    },
    async deleteDemand(id) {
      try {
        await demandService.deleteDemand(id)
        this.demands = this.demands.filter((d) => d._id !== id)
        return true
      } catch (err) {
        this.error = err.message || 'Erreur lors de la suppression de la demande'
        throw err
      }
    },
    async updateDemand(id, updateData) {
      this.isLoading = true
      this.error = null
      try {
        const response = await demandService.updateDemand(id, updateData)

        // Mettre à jour la demande dans le tableau local
        const index = this.demands.findIndex((d) => d._id === id)
        if (index !== -1) {
          this.demands[index] = { ...this.demands[index], ...updateData }
        }

        return response
      } catch (err) {
        this.error = err.message || 'Erreur lors de la mise à jour de la demande'
        throw err
      } finally {
        this.isLoading = false
      }
    },
    async getDemandById(id) {
      try {
        return await demandService.getDemandById(id)
      } catch (err) {
        this.error = err.message || 'Erreur lors de la récupération de la demande'
        throw err
      }
    },
    async fetchDemandsByEmployeeId(employeeId) {
      this.isLoading = true
      this.error = null
      try {
        const demands = await demandService.getDemandsByEmployeeId(employeeId)
        this.demands = demands
        return demands
      } catch (err) {
        this.error = err.message || "Erreur lors du chargement des demandes de l'employé"
        this.demands = []
        return []
      } finally {
        this.isLoading = false
      }
    },
  },
})
