import { defineStore } from 'pinia'
import { userService } from '@/services/userService'
import { push } from 'notivue'
import { useAuthStore } from './authStore'

export const useUserStore = defineStore('User Store', {
  state: () => ({
    users: [],
    isLoading: false,
    error: null,
  }),

  getters: {
    allUsers: (state) => state.users,
    hasError: (state) => !!state.error,
  },

  actions: {
    async fetchAllUsers() {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        this.error = 'Accès non autorisé'
        return
      }
      this.isLoading = true
      this.error = null

      try {
        const users = await userService.getAllUsers()
        this.users = users
        console.log('users: ', users)
        return users
      } catch (error) {
        this.error = error.message || 'Erreur lors de la récupération des utilisateurs'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async getUserById(id) {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        this.error = 'Accès non autorisé'
        return
      }

      this.isLoading = true
      this.error = null

      try {
        return await userService.getUserById(id)
      } catch (error) {
        this.error = error.message || `Erreur lors de la récupération de l'utilisateur #${id}`
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async createUser(userData) {
      const authStore = useAuthStore()
      if (!authStore.isAdmin) {
        this.error = 'Accès non autorisé'
        return
      }

      this.isLoading = true
      this.error = null

      try {
        const newUser = await userService.addUser(userData)
        this.users.push(newUser)
        return newUser
      } catch (error) {
        this.error = error.message || "Erreur lors de la création de l'utilisateur"
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async updateUser(id, userData) {
      const authStore = useAuthStore()
      if (!authStore.isAdmin) {
        this.error = 'Accès non autorisé'
        return
      }

      this.isLoading = true
      this.error = null

      try {
        const updatedUser = await userService.updateUser(id, userData)
        const index = this.users.findIndex((user) => user._id === id)
        if (index !== -1) {
          this.users[index] = updatedUser
        }
        return updatedUser
      } catch (error) {
        this.error = error.message || `Erreur lors de la mise à jour de l'utilisateur #${id}`
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async deleteUser(id) {
      const authStore = useAuthStore()
      if (!authStore.isAdmin) {
        this.error = 'Accès non autorisé'
        return
      }

      this.isLoading = true
      this.error = null

      try {
        await userService.deleteUser(id)
        this.users = this.users.filter((user) => user._id !== id)
        return true
      } catch (error) {
        this.error = error.message || `Erreur lors de la suppression de l'utilisateur #${id}`
        throw error
      } finally {
        this.isLoading = false
      }
    },

    resetError() {
      this.error = null
    },
  },
})
