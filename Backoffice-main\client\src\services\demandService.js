import api from '@/api'

export const demandService = {
  async getAllDemands() {
    try {
      console.log('Appel API: getAllDemands')
      const response = await api.get('/demands')
      console.log('Réponse API getAllDemands:', response.data)
      return response.data
    } catch (error) {
      console.error('Erreur API getAllDemands:', error)
      throw new Error(
        error.response?.data?.message || 'Erreur lors de la récupération des demandes',
      )
    }
  },

  async getDemandById(id) {
    try {
      const response = await api.get(`/demands/${id}`)
      return response.data
    } catch (error) {
      throw new Error(
        error.response?.data?.message || 'Erreur lors de la récupération de la demande',
      )
    }
  },

  async addDemand(demandData) {
    try {
      const response = await api.post('/demands', demandData)
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la création de la demande')
    }
  },

  async updateDemand(id, updateData) {
    try {
      const response = await api.put(`/demands/${id}`, updateData)
      return response.data
    } catch (error) {
      throw new Error(
        error.response?.data?.message || 'Erreur lors de la mise à jour de la demande',
      )
    }
  },

  async deleteDemand(id) {
    try {
      const response = await api.delete(`/demands/${id}`)
      return response.data
    } catch (error) {
      throw new Error(
        error.response?.data?.message || 'Erreur lors de la suppression de la demande',
      )
    }
  },

  async getDemandsByEmployeeId(employeeId) {
    try {
      const response = await api.get(`/demands/employee/${employeeId}`)
      return response.data
    } catch (error) {
      throw new Error(
        error.response?.data?.message || "Erreur lors de la récupération des demandes de l'employé",
      )
    }
  },

  // Ajout d'une méthode pour récupérer les demandes par statut
  async getDemandsByStatus(status) {
    try {
      console.log(`Appel API: getDemandsByStatus(${status})`)
      const response = await api.get(`/demands?status=${status}`)
      console.log(`Réponse API getDemandsByStatus(${status}):`, response.data)
      return response.data
    } catch (error) {
      console.error(`Erreur API getDemandsByStatus(${status}):`, error)
      throw new Error(
        error.response?.data?.message ||
          `Erreur lors de la récupération des demandes avec statut ${status}`,
      )
    }
  },

  // Ajout d'une méthode pour récupérer les demandes en attente
  async getPendingDemands() {
    return this.getDemandsByStatus('EN_ATTENTE')
  },
}
