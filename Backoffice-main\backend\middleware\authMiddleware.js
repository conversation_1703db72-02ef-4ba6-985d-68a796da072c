/**
 * Authentication middleware to protect routes
 * Verifies if a user is logged in via session
 */
const authMiddleware = (req, res, next) => {
  console.log("Auth Middleware - Session:", req.session);
  console.log("Auth Middleware - User:", req.session?.user);

  // Check if user is authenticated via session
  if (!req.session || !req.session.user) {
    console.log("Auth Middleware - Utilisateur non authentifié");

    // Pour le développement, vous pouvez commenter cette partie pour permettre les requêtes sans authentification
    // return res.status(401).json({
    //   message: "Authentication required",
    //   authenticated: false,
    // });

    // Simuler un utilisateur pour le développement
    req.user = {
      _id: "65f5e1f5e1f5e1f5e1f5e1f5", // ID fictif pour le développement
      role: "admin",
    };
  } else {
    // Assigner l'utilisateur de la session à req.user
    req.user = req.session.user;
    console.log("Auth Middleware - Utilisateur authentifié:", req.user);
  }

  // User is authenticated, proceed to the next middleware/route handler
  next();
};

module.exports = authMiddleware;
