{"name": "client", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write src/"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@mdi/font": "^7.4.47", "@tailwindcss/vite": "^4.1.6", "axios": "^1.9.0", "daisyui": "^5.0.35", "file-saver": "^2.0.5", "html-pdf-node": "^1.0.8", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "multer": "^1.4.5-lts.2", "notivue": "^2.4.5", "pinia": "^3.0.1", "puppeteer": "^24.9.0", "tailwindcss": "^4.1.6", "vue": "^3.5.13", "vue-cal": "^4.8.1", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "prettier": "3.5.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}