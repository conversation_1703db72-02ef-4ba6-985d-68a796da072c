const userService = require("../services/userService");

const createUser = async (req, res) => {
  try {
    const newUser = await userService.createUser(req.body);
    res.status(201).json(newUser);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Erreur lors de la création de l'utilisateur", error });
  }
};

const getUsers = async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.status(200).json(users);
  } catch (error) {
    res.status(500).json({
      message: "Erreur lors de la récupération des utilisateurs",
      error,
    });
  }
};

const getUserById = async (req, res) => {
  try {
    const user = await userService.getUserById(req.params.id);
    if (!user)
      return res.status(404).json({ message: "Utilisateur non trouvé" });
    res.status(200).json(user);
  } catch (error) {
    res.status(500).json({
      message: "Erreur lors de la récupération de l'utilisateur",
      error,
    });
  }
};

const updateUser = async (req, res) => {
  try {
    const userId = req.params.id;
    const updateData = {
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      email: req.body.email,
      isAdmin: req.body.isAdmin,
    };
    if (req.file) {
      updateData.profileImage = "/uploads/" + req.file.filename; // adapte selon ton dossier d'upload
    }
    const updatedUser = await userService.updateUser(userId, updateData);
    if (!updatedUser)
      return res.status(404).json({ message: "Utilisateur non trouvé" });
    res.status(200).json(updatedUser);
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: "Erreur lors de la mise à jour de l'utilisateur",
      error: err,
    });
  }
};

const deleteUser = async (req, res) => {
  try {
    const deleted = await userService.deleteUser(req.params.id);
    if (!deleted)
      return res.status(404).json({ message: "Utilisateur non trouvé" });
    res.status(200).json({ message: "Utilisateur supprimé avec succès" });
  } catch (error) {
    res.status(500).json({
      message: "Erreur lors de la suppression de l'utilisateur",
      error,
    });
  }
};

// Add this method to handle profile image uploads
const path = require("path");

const uploadProfileImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: "Aucun fichier n'a été téléchargé" });
    }
    const userId = req.params.id;
    const profileImage = `/uploads/profiles/${req.file.filename}`;

    const updatedUser = await userService.updateUser(userId, { profileImage });
    res.json({ user: updatedUser });
  } catch (error) {
    res
      .status(500)
      .json({ message: error.message || "Erreur lors de l'upload de l'image" });
  }
};

module.exports = {
  createUser,
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  uploadProfileImage,
};
