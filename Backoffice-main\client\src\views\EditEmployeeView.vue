<template>
    <div class="edit-employee-container">
        <h2>Modifier un employé</h2>
        <div class="form-row">
            <label>Image de profil</label>
            <ProfileImage v-if="form && form._id" :userId="form._id" :profileImage="form.profileImage || ''"
                :editable="true" @update:profileImage="form.profileImage = $event" />
        </div>
        <FormBase v-if="form" :fields="formFields" :validation-rules="validationRules" :initial-values="form"
            buttonText="Enregistrer" @onSubmit="handleSubmit" />
    </div>
</template>

<script>
import { useEmployeeStore } from '@/stores/employeeStore';
import { push } from 'notivue';
import FormBase from '@/components/Form.vue';
import ProfileImage from '@/components/ProfileImage.vue';

export default {
    name: 'EditEmployeeView',
    components: { FormBase, ProfileImage },
    data() {
        return {
            form: null,
            formFields: [
                { name: 'firstName', label: 'Prénom', type: 'text', placeholder: 'Entrez le prénom' },
                { name: 'lastName', label: 'Nom', type: 'text', placeholder: 'Entrez le nom' },
                {
                    name: 'gender', label: 'Genre', type: 'radio', options: [
                        { value: 'Homme', label: 'Homme' }, { value: 'Femme', label: 'Femme' }
                    ]
                },
                { name: 'birthDate', label: 'Date de naissance', type: 'date', placeholder: '' },
                { name: 'phone', label: 'Téléphone', type: 'text', placeholder: 'Entrez le téléphone' },
                { name: 'email', label: 'Email', type: 'email', placeholder: 'Entrez l\'email' },
                { name: 'address', label: 'Adresse', type: 'text', placeholder: 'Entrez l\'adresse' },
                { name: 'department', label: 'Département', type: 'text', placeholder: 'Entrez le département' },
                { name: 'hireDate', label: 'Date d\'embauche', type: 'date', placeholder: '' },
                {
                    name: 'status', label: 'Statut', type: 'radio', options: [
                        { value: 'Actif', label: 'Actif' }, { value: 'Inactif', label: 'Inactif' }
                    ]
                },
                { name: 'avatar', label: 'Avatar', type: 'file' }
            ],
            validationRules: {
                firstName: [{ required: true, message: 'Le prénom est requis' }],
                lastName: [{ required: true, message: 'Le nom est requis' }],
                gender: [{ required: true, message: 'Le genre est requis' }],
                birthDate: [{ required: true, message: 'La date de naissance est requise' }],
                phone: [{ required: true, message: 'Le téléphone est requis' }],
                email: [
                    { required: true, message: 'L\'email est requis' },
                    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: 'Veuillez entrer une adresse email valide' }
                ],
                address: [{ required: true, message: 'L\'adresse est requise' }],
                department: [{ required: true, message: 'Le département est requis' }],
                hireDate: [{ required: true, message: 'La date d\'embauche est requise' }],
                status: [{ required: true, message: 'Le statut est requis' }]
            },
            employeeStore: useEmployeeStore()
        };
    },
    mounted() {
        const id = this.$route.params.id;
        this.employeeStore.getEmployeeById(id).then(employee => {
            if (employee) {
                this.form = { ...employee };
            }
        });
    },
    methods: {
        async handleSubmit(formData) {
            try {
                await this.employeeStore.updateEmployee(this.form._id, formData);
                push.success({ title: 'Succès', message: 'Employé modifié avec succès' });
                this.$router.push('/employees');
            } catch (error) {
                push.error({ title: 'Erreur', message: error.message || 'Erreur lors de la modification' });
            }
        }
    }
};
</script>

<style scoped>
.edit-employee-container {
    max-width: 500px;
    margin: 2rem auto;
    background: #fff;
    border-radius: 1rem;
    padding: 2rem 2.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
}

h2 {
    text-align: center;
    color: #0c0c0d;
    margin-bottom: 1.5rem;
}

.form-row {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.avatar-img {
    margin-top: 0.5rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #22c55e;
}
</style>