const Contract = require("../models/Contract");

const createContract = async (data) => {
  try {
    console.log("Service - Données reçues pour création:", data);
    const contract = new Contract(data);
    const savedContract = await contract.save();
    console.log("Service - Contrat sauvegardé:", savedContract);
    return savedContract;
  } catch (error) {
    console.error("Service - Erreur lors de la création du contrat:", error);
    throw error;
  }
};

const getAllContracts = async () => {
  try {
    return await Contract.find().sort({ createdAt: -1 });
  } catch (error) {
    console.error(
      "Service - Erreur lors de la récupération des contrats:",
      error
    );
    throw error;
  }
};

const getContractsByEmployeeId = async (employeeId) => {
  try {
    console.log("Service - Recherche des contrats pour l'employé:", employeeId);
    // Rechercher par le champ employeeId
    const contracts = await Contract.find({
      $or: [{ employeeId: employeeId }, { employee: employeeId }],
    }).sort({ createdAt: -1 });
    console.log("Service - Contrats trouvés:", contracts);
    return contracts;
  } catch (error) {
    console.error(
      "Service - Erreur lors de la récupération des contrats par employé:",
      error
    );
    throw error;
  }
};

const getContractById = async (id) => {
  try {
    return await Contract.findById(id);
  } catch (error) {
    console.error(
      "Service - Erreur lors de la récupération du contrat:",
      error
    );
    throw error;
  }
};

const updateContract = async (id, data) => {
  try {
    return await Contract.findByIdAndUpdate(id, data, { new: true });
  } catch (error) {
    console.error("Service - Erreur lors de la mise à jour du contrat:", error);
    throw error;
  }
};

const deleteContract = async (id) => {
  try {
    return await Contract.findByIdAndDelete(id);
  } catch (error) {
    console.error("Service - Erreur lors de la suppression du contrat:", error);
    throw error;
  }
};

module.exports = {
  createContract,
  getAllContracts,
  getContractsByEmployeeId,
  getContractById,
  updateContract,
  deleteContract,
};
