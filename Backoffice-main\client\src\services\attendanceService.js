import api from '@/api'

export const attendanceService = {
  async getAttendancesByEmployeeId(id) {
    try {
      const response = await api.get(`/attendances/${id}`, { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error('Erreur lors de la récupération des présences', error)
    }
  },
  async getTodayAttendances(date) {
    try {
      const response = await api.get(`/attendances/date/${date}`, { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error('Erreur lors de la récupération des présences du jour', error)
    }
  },
}
