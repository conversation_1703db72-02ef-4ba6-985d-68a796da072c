<template>
    <div class="login-container">
        <div class="login-card">
            <div class="logo-container">
                <img src="@/assets/logo.png" alt="BackOffice EMS" class="logo"
                    onerror="this.src='https://ui-avatars.com/api/?name=EMS&size=128&background=4f46e5&color=fff'" />
            </div>
            <h1 class="login-title">Se Connecter</h1>

            <FormBase :fields="loginFields" :validation-rules="validationRules" buttonText="Login"
                @onSubmit="handleLogin" />

            <div class="login-footer">
                <p>Don't have an account? Contact your administrator.</p>
            </div>
        </div>
    </div>
</template>

<script>
import FormBase from '@/components/Form.vue';
import { useAuthStore } from '@/stores/authStore';
import { mapActions } from 'pinia';
import { push } from 'notivue';

export default {
    name: 'LoginView',
    components: {
        FormBase
    },
    data() {
        return {
            loginFields: [
                {
                    name: 'email',
                    label: 'Email',
                    type: 'email',
                    placeholder: 'Entrez votre email'
                },
                {
                    name: 'password',
                    label: 'Mot de passe',
                    type: 'password',
                    placeholder: 'Entrez votre mot de passe'
                }
            ],
            validationRules: {
                email: [
                    { required: true, message: 'L\'email est requis' },
                    {
                        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                        message: 'Veuillez entrer une adresse email valide'
                    }
                ],
                password: [
                    { required: true, message: 'Le mot de passe est requis' },
                    { minLength: 6, message: 'Le mot de passe doit contenir au moins 6 caractères' }
                ]
            }
        };
    },
    methods: {
        ...mapActions(useAuthStore, ['login']),

        async handleLogin(formData) {
            try {
                await this.login(formData.email, formData.password);
                this.$router.push('/dashboard'); // Redirige vers le tableau de bord
            } catch (error) {
                console.error('Login failed:', error);
                push.error({
                    title: 'Error',
                    type: 'error',
                    message: 'Login failed. Please check your credentials.'
                });
            }
        }
    }
};
</script>

<style scoped>
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: hsl(var(--b2));
    padding: 1rem;
}

.login-card {
    width: 100%;
    max-width: 400px;
    background-color: hsl(var(--b1));
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.logo-container {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
}

.login-title {
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1.5rem;
    color: hsl(var(--p));
}

.login-footer {
    margin-top: 1.5rem;
    text-align: center;
    font-size: 0.875rem;
    color: hsl(var(--bc) / 0.7);
}
</style>