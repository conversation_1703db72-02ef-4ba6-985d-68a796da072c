<template>
    <div class="max-w-xl w-full mx-auto bg-white rounded-2xl shadow-xl p-8 mt-10">
        <h1 class="text-2xl sm:text-3xl font-bold text-primary text-center mb-6">Ajouter un département</h1>
        <FormBase :fields="formFields" :validation-rules="validationRules" buttonText="Ajouter"
            @onSubmit="handleSubmit" />
    </div>
</template>

<script>
import { useDepartmentStore } from '@/stores/departmentStore';
import FormBase from '@/components/Form.vue';

export default {
    name: 'AddDepartmentView',
    components: { FormBase },
    data() {
        return {
            formFields: [
                {
                    name: 'name',
                    label: 'Nom du département',
                    type: 'text',
                    placeholder: 'Entrez le nom du département'
                },
                {
                    name: 'employeeCount',
                    label: 'Nombre des employés',
                    type: 'number',
                    placeholder: 'Entrez le nombre d\'employés'
                },

            ],
            validationRules: {
                name: [
                    { required: true, message: 'Le nom du département est requis' }
                ],
                employeeCount: [
                    { required: true, message: 'Le nombre des employés est requis' }
                ]
            },
            departmentStore: useDepartmentStore()
        };
    },
    methods: {
        async handleSubmit(formData) {
            try {
                await this.departmentStore.addDepartment(formData);
                alert('Département ajouté !');
                this.$router.push('/departments');
            } catch (error) {
                alert('Erreur lors de l\'ajout du département');
            }
        }
    }
};
</script>
