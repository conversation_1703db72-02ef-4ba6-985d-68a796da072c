const express = require("express");
const router = express.Router();
const pdfService = require("../services/pdfService");
const Employee = require("../models/Employee");
const Demand = require("../models/Demand");
// const authMiddleware = require('../middleware/authMiddleware');

/**
 * @route   POST /api/pdf/demand/:id
 * @desc    Génère un PDF pour une demande
 * @access  Public (temporairement)
 */
router.post("/demand/:id", async (req, res) => {
  try {
    const demandId = req.params.id;
    console.log("Génération de PDF pour la demande:", demandId);

    // Récupérer la demande
    const demand = await Demand.findById(demandId);
    if (!demand) {
      console.log("Demande non trouvée:", demandId);
      return res.status(404).json({ message: "Demande non trouvée" });
    }

    // Afficher l'objet demand complet pour le débogage
    console.log("Demande trouvée (objet complet):", JSON.stringify(demand));

    // Récupérer l'employeeId de différentes manières possibles
    const demandObj = demand.toObject ? demand.toObject() : demand;
    console.log("Demande convertie en objet:", demandObj);

    // Essayer d'accéder à employeeId de différentes façons
    const employeeId =
      demandObj.employeeId ||
      demandObj.employee ||
      demand.get?.("employeeId") ||
      demand.get?.("employee");
    console.log("ID employé extrait:", employeeId);

    if (!employeeId) {
      return res
        .status(404)
        .json({ message: "ID d'employé introuvable dans la demande" });
    }

    const employee = await Employee.findById(employeeId);
    if (!employee) {
      console.log("Employé non trouvé:", employeeId);
      return res.status(404).json({ message: "Employé non trouvé" });
    }
    console.log("Employé trouvé:", employee.firstName, employee.lastName);

    // Générer le PDF
    console.log("Génération du PDF...");
    const pdfBuffer = await pdfService.generateDemandPdf(demand, employee);
    console.log("PDF généré avec succès, taille:", pdfBuffer.length);

    // Envoyer le PDF
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=demande_${demand.type}_${employee.lastName}.pdf`
    );
    res.send(pdfBuffer);
    console.log("PDF envoyé avec succès");
  } catch (error) {
    console.error("Erreur lors de la génération du PDF:", error);
    res
      .status(500)
      .json({
        message: "Erreur lors de la génération du PDF",
        error: error.message,
      });
  }
});

module.exports = router;
