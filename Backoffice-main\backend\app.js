const express = require("express");
const connectDB = require("./conf/db");
require("dotenv").config();
const cors = require("cors");
const path = require("path");
const events = require("events");

// Increase the maximum number of listeners to prevent warnings
events.EventEmitter.defaultMaxListeners = 15;

const server = express();

const employeeRoutes = require("./routes/employeeRoutes");
const departmentRoutes = require("./routes/departmentRoutes");
const userRoutes = require("./routes/userRoutes");
const contractRoutes = require("./routes/contractRoutes");
const demandRoutes = require("./routes/demandRoutes");
const attendanceRoutes = require("./routes/attendanceRoutes");
const authRoutes = require("./routes/authRoutes");
const docsRoutes = require("./routes/docsRoutes");
const session = require("express-session");
const pdfRoutes = require("./routes/pdfRoutes");

server.use(express.json());
server.use(
  cors({
    origin: "http://localhost:5173",
    credentials: true, // Allow credentials (cookies, authorization headers)
  })
);
// Serve static files from the public directory
server.use("/uploads", express.static(path.join(__dirname, "public/uploads")));

server.use(
  session({
    secret: process.env.SESSION_SECRET,
    resave: false,
    saveUninitialized: false,

    cookie: { secure: false },
  })
);

connectDB();

server.use("/api/employees", employeeRoutes);
server.use("/api/departments", departmentRoutes);
server.use("/api/users", userRoutes);
server.use("/api/contracts", contractRoutes);
server.use("/api/demands", demandRoutes);
server.use("/api/attendances", attendanceRoutes);
server.use("/api/auth", authRoutes);
server.use("/docs", docsRoutes);
server.use("/api/pdf", pdfRoutes);

// Update the root route to redirect to the docs
server.get("/", (req, res) => {
  res.redirect("/docs");
});
const PORT = process.env.PORT || 6000;
server.listen(PORT, () => {
  console.log(`✅ le serveur est démarré sur http://localhost:${PORT}`);
});
