{"name": "backend", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "seed": "node scripts/seedUsers.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "fs-extra": "^11.3.0", "handlebars": "^4.7.8", "mongoose": "^8.13.2", "multer": "^1.4.5-lts.2", "puppeteer": "^24.9.0"}, "devDependencies": {"nodemon": "^3.1.9"}}