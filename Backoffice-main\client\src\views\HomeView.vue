<template>
  <div class="min-h-screen bg-base-200">
    <!-- Header -->
    <div class="relative bg-white shadow-md rounded-b-2xl mb-8">
      <div class="flex flex-col md:flex-row justify-between items-center px-8 py-6">
        <div>
          <h1 class="text-4xl font-bold text-primary mb-1">Bienvenue!</h1>
          <div class="text-2xl font-semibold text-gray-700 uppercase tracking-wide">
            {{ currentUser?.firstName }} {{ currentUser?.lastName }}
          </div>
        </div>
        <div class="flex flex-col md:flex-row gap-4 mt-4 md:mt-0">
          <!-- Carte Statistiques employés -->
          <div class="card w-80 bg-base-100 border border-primary shadow">
            <div class="card-body flex flex-row items-center gap-4 p-4">
              <div class="bg-primary/10 rounded-full p-3">
                <i class="fas fa-users text-primary text-2xl"></i>
              </div>
              <div>
                <div class="text-xs text-gray-500 font-semibold uppercase">Statistiques employés</div>
                <div class="text-lg font-bold text-primary">
                  Total : {{ totalEmployees }}
                </div>
                <div class="text-sm mt-1">
                  <span class="text-info font-semibold">Femmes :</span> {{ femaleEmployees }}
                  <span class="mx-2">|</span>
                  <span class="text-success font-semibold">Hommes :</span> {{ maleEmployees }}
                </div>
              </div>
            </div>
          </div>
          <!-- Présence Card -->
          <div class="card w-64 bg-base-100 border border-success shadow">
            <div class="card-body flex flex-row items-center gap-4 p-4">
              <div class="bg-success/10 rounded-full p-3">
                <i class="fas fa-calendar-check text-success text-2xl"></i>
              </div>
              <div>
                <div class="text-xs text-success font-semibold uppercase">Présences</div>
                <div class="text-2xl font-bold text-success">{{ totalPresences }}</div>
                <div class="text-xs text-red-500 font-semibold">{{ absentsCount }} absents</div>
              </div>
            </div>
          </div>
          <!-- Employés Card -->
          <div class="card w-64 bg-base-100 border border-info shadow">
            <div class="card-body flex flex-row items-center gap-4 p-4">
              <div class="bg-info/10 rounded-full p-3">
                <i class="fas fa-users text-info text-2xl"></i>
              </div>
              <div>
                <div class="text-xs text-info font-semibold uppercase">Employés</div>
                <div class="text-2xl font-bold text-info">{{ employees.length }}</div>
                <div class="text-xs text-gray-500">Contrats actifs</div>
              </div>
            </div>
          </div>
          <!-- Demandes Card -->
          <div class="card w-64 bg-base-100 border border-primary shadow">
            <div class="card-body flex flex-row items-center gap-4 p-4">
              <div class="bg-primary/10 rounded-full p-3">
                <i class="fas fa-file-alt text-primary text-2xl"></i>
              </div>
              <div>
                <div class="text-xs text-primary font-semibold uppercase">Demandes</div>
                <div class="text-2xl font-bold text-primary">{{ pendingDemandsCount }}</div>
                <div class="text-xs text-gray-500">Nouvelles demandes</div>
              </div>
            </div>
          </div>
        </div>
        <div class="absolute top-4 right-8 flex items-center gap-2">
          <span class="font-semibold text-gray-700">{{ currentUser?.firstName }} {{ currentUser?.lastName }}</span>
          <span class="text-xs text-gray-400">ADMIN</span>
          <img :src="userAvatar" alt="avatar" class="w-10 h-10 rounded-full border-2 border-primary object-cover" />
        </div>
      </div>
      <div class="absolute inset-0 opacity-10 pointer-events-none"
        style="background:url('https://www.transparenttextures.com/patterns/diamond-upholstery.png');background-size:cover;">
      </div>

      <p v-if="demands.length === 0" class="text-center text-gray-400">Aucune demande en cours</p>
    </div>

    <!-- Indicators and Tables Section -->
    <div class="px-8 grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Absent Employees List -->
      <div>
        <h2 class="text-lg font-bold mb-2">EMPLOYÉS NON POINTÉS</h2>
        <div class="bg-white rounded-xl shadow p-2 max-h-80 overflow-y-auto">
          <ul v-if="employeesWithNoPresence && employeesWithNoPresence.length > 0">
            <li v-for="absent in employeesWithNoPresence" :key="absent._id"
              class="py-2 border-b last:border-b-0 flex items-center gap-2">
              <img :src="getAbsentAvatar(absent)" :alt="`${absent.firstName} ${absent.lastName}`"
                class="w-8 h-8 rounded-full object-cover border border-base-300" />
              <span class="font-medium">{{ absent.firstName }} {{ absent.lastName }}</span>
            </li>
          </ul>
          <div v-else class="text-gray-400 text-center py-4">Aucun employé absent aujourd'hui</div>
        </div>
      </div>
      <!-- Pending Requests List -->
      <div>
        <h2 class="text-lg font-bold mb-2">LISTE DES DEMANDES <span class="text-blue-600">EN ATTENTE</span></h2>



        <!-- Afficher les demandes traitées -->
        <div v-if="demandesAvecEmploye.length > 0">
          <Table :headers="demandsHeaders" :data="demandesAvecEmploye" :show-actions="false">
            <template #cell-requestedAt="{ row }">
              {{ new Date(row.requestedAt || row.createdAt).toLocaleDateString() }}
            </template>
            <template #cell-employeeFirstName="{ row }">
              {{ row.employeeFirstName }} {{ row.employeeLastName }}
            </template>
          </Table>
        </div>

        <div v-if="!pendingDemands || pendingDemands.length === 0" class="text-gray-400 text-center py-4">
          Aucune demande en attente
        </div>
      </div>

      <!-- Contracts List -->
      <div>
        <h2 class="text-lg font-bold mb-2">LISTE DES CONTRATS EXPIRANT DANS 30 JOURS</h2>
        <Table :headers="contractsHeaders" :data="contracts" :show-actions="false">
          <template #cell-endDate="{ row }">
            {{ new Date(row.endDate).toLocaleDateString() }}
          </template>
        </Table>
      </div>
    </div>
  </div>
</template>

<script>
import { useAuthStore } from '@/stores/authStore';
import { useEmployeeStore } from '@/stores/employeeStore';
import { useAttendanceStore } from '@/stores/attendanceStore';
import { useDemandStore } from '@/stores/demandStore';
import { useContractStore } from '@/stores/contractStore';
import { mapState, mapActions } from 'pinia';
import DataTable from '@/components/Table.vue';

export default {
  name: 'HomeView',
  components: {
    Table: DataTable
  },
  data() {
    return {
      totalEmployees: 0,
      femaleEmployees: 0,
      maleEmployees: 0,
      demands: [],
      employeesWithoutPresence: [] // Nouvelle propriété pour stocker les employés sans présence
    };
  },

  computed: {
    ...mapState(useAuthStore, ['user']),
    ...mapState(useEmployeeStore, ['employees']),
    ...mapState(useAttendanceStore, ['presences', 'absents']),
    ...mapState(useContractStore, ['contracts']),
    ...mapState(useDemandStore, ['demands', 'pendingDemands', 'pendingDemandsCount']),
    currentUser() {
      return this.user;
    },
    userAvatar() {
      if (this.currentUser && this.currentUser.profileImage) {
        if (this.currentUser.profileImage.startsWith('http')) {
          return this.currentUser.profileImage;
        }
        return `${import.meta.env.VITE_BASE_URL || ''}${this.currentUser.profileImage}`;
      }
      return `https://ui-avatars.com/api/?name=${this.currentUser?.firstName || 'A'}+${this.currentUser?.lastName || 'D'}&background=random`;
    },
    totalPresences() {
      return this.presences?.length || 0;
    },
    absentsCount() {
      return this.absents?.length || 0;
    },
    demandesAvecEmploye() {
      if (!this.employees || !this.pendingDemands) return [];

      console.log("Demandes en attente:", this.pendingDemands);

      const empMap = {};
      this.employees.forEach(emp => {
        empMap[emp._id] = emp;
      });

      return this.pendingDemands.map(demande => {
        const employeeId = demande.employeeId ||
          (demande.employee && demande.employee._id) ||
          demande.employee;
        const employe = empMap[employeeId] || {};

        return {
          ...demande,
          employeeFirstName: employe.firstName || '',
          employeeLastName: employe.lastName || ''
        };
      });
    },
    demandsHeaders() {
      return [
        { label: 'Type', value: 'type' },
        { label: 'Salarié', value: 'employeeFirstName' },
        { label: 'Date dépôt', value: 'requestedAt' },
        { label: 'Statut', value: 'status' }
      ];
    },
    contractsHeaders() {
      return [
        { label: 'Nom', value: 'lastName' },
        { label: 'Prénom', value: 'firstName' },
        { label: 'CIN', value: 'cin' },
        { label: 'Type Contrat', value: 'contractType' },
        { label: 'Date Fin Contrat', value: 'endDate' }
      ];
    },
    // Nouvelle computed property pour obtenir les employés sans présence
    employeesWithNoPresence() {
      if (!this.employees || !this.presences) return [];

      // Créer un ensemble des IDs des employés qui ont une présence
      const employeesWithPresenceIds = new Set(
        this.presences.map(presence => presence.employeeId)
      );

      // Filtrer les employés qui n'ont pas de présence
      return this.employees.filter(employee =>
        !employeesWithPresenceIds.has(employee._id)
      );
    }
  },

  methods: {
    ...mapActions(useDemandStore, ['fetchDemands', 'fetchPendingDemands']),
    ...mapActions(useContractStore, ['fetchContracts']),
    ...mapActions(useEmployeeStore, ['fetchAllEmployees']),
    ...mapActions(useAttendanceStore, ['fetchAttendancesbyEmployeeId']),
    getAbsentAvatar(absent) {
      if (absent.profileImage) {
        if (absent.profileImage.startsWith('http')) {
          return absent.profileImage;
        }
        return (import.meta.env.VITE_BASE_URL || '') + absent.profileImage;
      }
      return `https://ui-avatars.com/api/?name=${absent.firstName}+${absent.lastName}&background=random`;
    },
    async fetchAllData() {
      try {
        // Récupérer toutes les demandes
        await this.fetchPendingDemands();
        console.log("Demandes en attente récupérées:", this.pendingDemands);

        await this.fetchContracts();
        await this.fetchAllEmployees();

        // Récupérer les présences de tous les employés pour aujourd'hui
        await this.fetchTodayAttendances();

        // Mettre à jour la liste des employés sans présence
        this.employeesWithoutPresence = this.employeesWithNoPresence;
        console.log("Employés sans présence:", this.employeesWithoutPresence);
      } catch (error) {
        console.error("Erreur lors de la récupération des données:", error);
      }
    },

    // Nouvelle méthode pour récupérer les présences du jour
    async fetchTodayAttendances() {
      try {
        const attendanceStore = useAttendanceStore();
        // Récupérer les présences du jour
        await attendanceStore.fetchTodayAttendances();
      } catch (error) {
        console.error("Erreur lors de la récupération des présences du jour:", error);
      }
    }
  },
  async mounted() {
    await this.fetchAllData();

    this.$watch(
      () => this.employees,
      (employees) => {
        this.totalEmployees = employees.length;
        this.femaleEmployees = employees.filter(e => e.gender && e.gender.toLowerCase() === 'femme').length;
        this.maleEmployees = employees.filter(e => e.gender && e.gender.toLowerCase() === 'homme').length;
      },
      { immediate: true }
    );
  }
};
</script>

<style scoped>
.table {
  box-shadow: 0 2px 8px #e0e0e0;
}
</style>
