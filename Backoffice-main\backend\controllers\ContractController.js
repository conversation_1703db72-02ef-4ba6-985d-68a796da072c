const contractService = require("../services/contractService");
const fs = require("fs");
const path = require("path");
const multer = require("multer");

// Configuration de multer pour le stockage des fichiers
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, "../public/uploads/contracts");
    // Créer le répertoire s'il n'existe pas
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Générer un nom de fichier unique
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    cb(null, "contract-" + uniqueSuffix + ext);
  },
});

// Filtre pour les types de fichiers acceptés
const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(
      new Error("Type de fichier non supporté. Utilisez PDF, DOC ou DOCX."),
      false
    );
  }
};

// Initialisation de l'upload
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 5 * 1024 * 1024 }, // Limite à 5MB
}).single("contractFile");

const createContract = async (req, res) => {
  // Utiliser multer pour gérer l'upload de fichier
  upload(req, res, async function (err) {
    if (err instanceof multer.MulterError) {
      console.error("Erreur Multer:", err);
      return res.status(400).json({
        message: "Erreur lors de l'upload du fichier",
        error: err.message,
      });
    } else if (err) {
      console.error("Erreur d'upload:", err);
      return res.status(400).json({ message: err.message });
    }

    try {
      // Si aucun fichier n'a été uploadé
      if (!req.file) {
        return res
          .status(400)
          .json({ message: "Le document du contrat est requis" });
      }

      console.log("Données du formulaire:", req.body);
      console.log("Fichier uploadé:", req.file);

      // Préparer les données du contrat
      const contractData = {
        employeeId: req.body.employeeId,
        type: req.body.type,
        startDate: req.body.startDate,
        endDate: req.body.type === "CDD" ? req.body.endDate : null,
        salary: parseFloat(req.body.salary),
        position: req.body.position,
        fileUrl: `/uploads/contracts/${req.file.filename}`,
        fileName: {
          name: req.file.originalname,
          url: `/uploads/contracts/${req.file.filename}`,
          path: req.file.path,
        },
        expiresAt: req.body.type === "CDD" ? new Date(req.body.endDate) : null,
        createdBy: req.user && req.user._id ? req.user._id : null,
      };

      console.log("Données du contrat à sauvegarder:", contractData);

      const contract = await contractService.createContract(contractData);
      console.log("Contrat créé:", contract);

      // Renvoyer le contrat créé avec un message de succès
      res.status(201).json({
        message: "Contrat créé avec succès",
        contract: contract, // Assurez-vous que cette propriété est bien nommée "contract"
      });
    } catch (error) {
      console.error("Erreur lors de la création du contrat:", error);

      // En cas d'erreur, supprimer le fichier uploadé si existant
      if (req.file) {
        fs.unlink(req.file.path, (err) => {
          if (err)
            console.error("Erreur lors de la suppression du fichier:", err);
        });
      }

      res.status(500).json({
        message: "Erreur lors de la création du contrat",
        error: error.message || "Erreur inconnue",
      });
    }
  });
};

const getAllContracts = async (req, res) => {
  try {
    const contracts = await contractService.getAllContracts();
    res.status(200).json(contracts);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Erreur lors de la récupération des contrats", error });
  }
};

const getContractById = async (req, res) => {
  try {
    const contract = await contractService.getContractById(req.params.id);
    if (!contract)
      return res.status(404).json({ message: "Contrat non trouvé" });
    res.status(200).json(contract);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Erreur lors de la récupération du contrat", error });
  }
};

const updateContract = async (req, res) => {
  try {
    const updated = await contractService.updateContract(
      req.params.id,
      req.body
    );
    if (!updated)
      return res.status(404).json({ message: "Contrat non trouvé" });
    res.status(200).json(updated);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Erreur lors de la mise à jour du contrat", error });
  }
};

const deleteContract = async (req, res) => {
  try {
    const deleted = await contractService.deleteContract(req.params.id);
    if (!deleted)
      return res.status(404).json({ message: "Contrat non trouvé" });
    res.status(200).json({ message: "Contrat supprimé avec succès" });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Erreur lors de la suppression du contrat", error });
  }
};

module.exports = {
  createContract,
  getAllContracts,
  getContractById,
  updateContract,
  deleteContract,
};
