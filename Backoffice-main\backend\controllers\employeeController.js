const employeeService = require("../services/employeeService");
const Employee = require("../models/Employee");

const createEmployee = async (req, res) => {
  console.log(req.user);
  try {
    // On suppose que req.user contient l'utilisateur connecté (grâce à authMiddleware)
    const employeeData = {
      ...req.body,
      createdBy: req.user ? req.user._id : null, // ou req.user.id selon ton modèle
    };
    const newEmployee = await Employee.create(employeeData);
    res.status(201).json(newEmployee);
  } catch (error) {
    console.error(error); // ← vérifie ce qui s’affiche ici
    res
      .status(500)
      .json({ message: "Erreur lors de la création de l'employé", error });
  }
};

const getAllEmployees = async (req, res) => {
  try {
    const employees = await employeeService.getAllEmployees();
    res.status(200).json(employees);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Erreur lors de la récupération des employés", error });
  }
};

const getEmployeeById = async (req, res) => {
  try {
    const employee = await employeeService.getEmployeeById(req.params.id);
    if (!employee)
      return res.status(404).json({ message: "Employé non trouvé" });
    res.status(200).json(employee);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Erreur lors de la récupération de l'employé", error });
  }
};

const updateEmployee = async (req, res) => {
  try {
    const updated = await employeeService.updateEmployee(
      req.params.id,
      req.body
    );
    if (!updated)
      return res.status(404).json({ message: "Employé non trouvé" });
    res.status(200).json(updated);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Erreur lors de la mise à jour de l'employé", error });
  }
};

const deleteEmployee = async (req, res) => {
  try {
    const deleted = await employeeService.deleteEmployee(req.params.id);
    if (!deleted)
      return res.status(404).json({ message: "Employé non trouvé" });
    res.status(200).json({ message: "Employé supprimé avec succès" });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Erreur lors de la suppression de l'employé", error });
  }
};

module.exports = {
  createEmployee,
  getAllEmployees,
  getEmployeeById,
  updateEmployee,
  deleteEmployee,
};
