const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const attendanceSchema = new Schema({
  employeeId: {
    type: String,
    ref: "Employee",
    required: true,
  },
  date: {
    type: Date,
    required: true,
  },
  checkIn: {
    type: Date,
    required: true,
  },
  checkOut: {
    type: Date,
    required: false, // Optionnel car l'employé peut ne pas avoir encore fait son checkout
  },
  status: {
    type: String,
    required: true,
  },
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Méthode virtuelle pour calculer les heures travaillées
attendanceSchema.virtual('hoursWorked').get(function() {
  if (!this.checkOut) return 0;
  
  const checkInTime = new Date(this.checkIn).getTime();
  const checkOutTime = new Date(this.checkOut).getTime();
  
  // Calcul de la différence en heures
  const diffInMs = checkOutTime - checkInTime;
  const diffInHours = diffInMs / (1000 * 60 * 60);
  
  return parseFloat(diffInHours.toFixed(2)); // Arrondi à 2 décimales
});

module.exports = mongoose.model("Attendance", attendanceSchema);
