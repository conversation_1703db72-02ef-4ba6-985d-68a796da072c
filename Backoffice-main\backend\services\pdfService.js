const puppeteer = require("puppeteer");
const fs = require("fs").promises;
const path = require("path");
const Handlebars = require("handlebars");

/**
 * Génère un PDF à partir d'un template HTML
 * @param {String} htmlContent - Le contenu HTML
 * @param {Object} options - Les options de génération
 * @returns {Promise<Buffer>} - Le buffer du PDF généré
 */
async function generatePdfFromHtml(htmlContent, options = {}) {
  console.log("Lancement de Puppeteer...");
  const browser = await puppeteer.launch({
    headless: "new",
    args: ["--no-sandbox", "--disable-setuid-sandbox"],
  });

  try {
    console.log("Création d'une nouvelle page...");
    const page = await browser.newPage();
    console.log("Définition du contenu HTML...");
    await page.setContent(htmlContent, { waitUntil: "networkidle0" });

    const pdfOptions = {
      format: options.format || "A4",
      margin: options.margin || {
        top: "20mm",
        right: "20mm",
        bottom: "20mm",
        left: "20mm",
      },
      printBackground: true,
      preferCSSPageSize: true,
    };

    console.log("Génération du PDF avec options:", pdfOptions);
    const pdfBuffer = await page.pdf(pdfOptions);
    console.log("PDF généré avec succès, taille:", pdfBuffer.length);
    return pdfBuffer;
  } catch (error) {
    console.error("Erreur lors de la génération du PDF:", error);
    throw error;
  } finally {
    console.log("Fermeture du navigateur...");
    await browser.close();
  }
}

/**
 * Calcule la durée en jours entre deux dates
 * @param {Date} startDate - Date de début
 * @param {Date} endDate - Date de fin
 * @returns {Number} - Nombre de jours
 */
function calculateDuration(startDate, endDate) {
  if (!startDate || !endDate) return null;
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays + 1; // +1 pour inclure le jour de début
}

/**
 * Détermine la classe CSS pour le statut
 * @param {String} status - Statut de la demande
 * @returns {String} - Classe CSS
 */
function getStatusClass(status) {
  if (!status) return "status-pending";

  const statusLower = status.toLowerCase();
  if (
    statusLower.includes("accept") ||
    statusLower === "approuvé" ||
    statusLower === "validé"
  ) {
    return "status-approved";
  }
  if (
    statusLower.includes("refus") ||
    statusLower === "rejeté" ||
    statusLower === "rejetée"
  ) {
    return "status-rejected";
  }
  return "status-pending";
}

/**
 * Génère un PDF pour une demande
 * @param {Object} demand - L'objet demande
 * @param {Object} employee - L'objet employé
 * @returns {Promise<Buffer>} - Le buffer du PDF généré
 */
async function generateDemandPdf(demand, employee) {
  console.log("Génération du PDF pour la demande:", demand._id);

  try {
    // Lire le template depuis le fichier
    const templatePath = path.join(
      __dirname,
      "../templates/demand-template.html"
    );
    const templateContent = await fs.readFile(templatePath, "utf8");

    // Compiler le template avec Handlebars
    const template = Handlebars.compile(templateContent);

    // Préparer les données pour le template
    const data = {
      demandId: demand._id.toString().slice(-6).toUpperCase(),
      demandType: demand.type || "Congé",
      emissionDate: formatDate(new Date()),
      firstName: employee.firstName || "",
      lastName: employee.lastName || "",
      email: employee.email || "",
      department: employee.department || "Non spécifié",
      requestedAt: formatDate(demand.requestedAt),
      status: demand.status || "En attente",
      statusClass: getStatusClass(demand.status),
      startDate: demand.startDate ? formatDate(demand.startDate) : null,
      endDate: demand.endDate ? formatDate(demand.endDate) : null,
      duration: calculateDuration(demand.startDate, demand.endDate),
      reason: demand.reason || null,
      managerName: null, // À compléter si disponible
      currentYear: new Date().getFullYear(),
    };

    // Générer le HTML avec les données
    const htmlContent = template(data);

    // Générer le PDF
    console.log("HTML généré, génération du PDF...");
    return await generatePdfFromHtml(htmlContent);
  } catch (error) {
    console.error("Erreur lors de la génération du template:", error);

    // Fallback en cas d'erreur avec le template
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Demande ${demand.type}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { color: #333; }
          .info { margin-bottom: 20px; }
          .label { font-weight: bold; }
        </style>
      </head>
      <body>
        <h1>Demande de ${demand.type}</h1>
        <div class="info">
          <p><span class="label">Employé:</span> ${employee.firstName} ${
      employee.lastName
    }</p>
          <p><span class="label">Email:</span> ${employee.email}</p>
          <p><span class="label">Date de demande:</span> ${formatDate(
            demand.requestedAt
          )}</p>
          <p><span class="label">Statut:</span> ${demand.status}</p>
          ${
            demand.startDate
              ? `<p><span class="label">Date de début:</span> ${formatDate(
                  demand.startDate
                )}</p>`
              : ""
          }
          ${
            demand.endDate
              ? `<p><span class="label">Date de fin:</span> ${formatDate(
                  demand.endDate
                )}</p>`
              : ""
          }
          ${
            demand.reason
              ? `<p><span class="label">Motif:</span> ${demand.reason}</p>`
              : ""
          }
        </div>
        <p>Document généré le ${formatDate(new Date())}</p>
      </body>
      </html>
    `;

    return await generatePdfFromHtml(htmlContent);
  }
}

/**
 * Formate une date en format français (DD/MM/YYYY)
 * @param {Date|String} date - La date à formater
 * @returns {String} - La date formatée
 */
function formatDate(date) {
  if (!date) return "";
  const d = new Date(date);
  if (isNaN(d.getTime())) return "Date invalide";
  return d.toLocaleDateString("fr-FR");
}

module.exports = {
  generatePdfFromHtml,
  generateDemandPdf,
};
