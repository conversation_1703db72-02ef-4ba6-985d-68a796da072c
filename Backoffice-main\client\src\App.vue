<template>
  <Navbar v-if="!hideNavbar" />

  <RouterView />
  <Notivue v-slot="item">
    <Notification :item="item" />
  </Notivue>
</template>

<script>
import { Notivue, Notification } from 'notivue';
import Navbar from './components/Navbar.vue';
import { push } from 'notivue';
import { computed } from 'vue';
import { useAuthStore } from './stores/authStore';
import { mapActions, mapState } from 'pinia';

export default {
  components: {
    Notivue,
    Notification,
    Navbar
  },
  data() {
    return {
      hideNavbar: false
    };
  },
  computed: {
    hideNavbar() {
      return this.$route.meta.hideNavbar;
    },
    ...mapState(useAuthStore, ['currentUser']),
  },
  methods: {
    ...mapActions(useAuthStore, ['checkAuth', 'logout']),

    async handleLogout() {
      try {
        await this.logout();
        this.$router.push({ name: 'loginForm' });
      } catch (error) {
        console.error('Failed to logout:', error);
        push.error({
          title: 'Error',
          type: 'error',
          message: 'Failed to logout',
        });
      }
    },

    handleLogin() {
      this.$router.push({ name: 'loginForm' });
    },
  },
  // async mounted() {
  //   const value = await this.checkAuth();
  //   console.log("checkAuth value: ", value);

  // },
};
</script>