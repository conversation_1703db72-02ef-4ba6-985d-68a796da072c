require('dotenv').config({ path: '../.env' });
const mongoose = require('mongoose');
const connectDB = require('../conf/db');

// Connect to database
connectDB();

// Import seed functions
const seedUsers = require('./seedUsers');

// Run all seed functions
const runAllSeeds = async () => {
  try {
    console.log('Starting database seeding...');
    
    // Run user seeds
    await seedUsers();
    
    console.log('All seeds completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error running seeds:', error);
    process.exit(1);
  }
};

// Run the seeding process
runAllSeeds();