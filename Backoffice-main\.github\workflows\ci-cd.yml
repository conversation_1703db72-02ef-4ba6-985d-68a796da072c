name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    services:
      mongo:
        image: mongo:6
        ports: [27017:27017]

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Install backend dependencies
        run: |
          cd backend
          npm install

      - name: Install frontend dependencies
        run: |
          cd client
          npm install

      - name: Run backend tests
        run: |
          cd backend
          npm test

      - name: Run frontend tests
        run: |
          cd client
          npm run test

      - name: Build frontend
        run: |
          cd client
          npm run build

  docker-deploy:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker images
        run: |
          docker-compose build
          docker-compose push