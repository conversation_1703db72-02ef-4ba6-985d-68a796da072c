const mongoose = require("mongoose");
const User = require("../models/User");
const fs = require("fs-extra");
const path = require("path");
const bcrypt = require("bcrypt");
require("dotenv").config();

// Connect to MongoDB
mongoose
  .connect(process.env.MONGODB_URI)
  .then(() => console.log("MongoDB connected for seeding"))
  .catch((err) => {
    console.error("MongoDB connection error:", err);
    process.exit(1);
  });

// Ensure the uploads directory exists
const uploadsDir = path.join(__dirname, "../public/uploads/profile-images");
fs.ensureDirSync(uploadsDir);

// Sample profile images to copy (you'll need to provide these images)
const sampleImages = [
  {
    source: path.join(__dirname, "../seeds/images/admin.jpg"),
    dest: "admin.jpg",
  },
  {
    source: path.join(__dirname, "../seeds/images/user1.png"),
    dest: "user1.png",
  },
  {
    source: path.join(__dirname, "../seeds/images/user2.jpg"),
    dest: "user2.jpg",
  },
];

// Copy sample images to uploads directory
const copyImages = async () => {
  try {
    for (const image of sampleImages) {
      // Check if source image exists
      if (fs.existsSync(image.source)) {
        await fs.copy(image.source, path.join(uploadsDir, image.dest));
        console.log(
          `Copied ${image.source} to ${path.join(uploadsDir, image.dest)}`
        );
      } else {
        console.warn(`Warning: Source image ${image.source} does not exist`);
      }
    }
  } catch (error) {
    console.error("Error copying images:", error);
  }
};

// Sample users data
const users = [
  {
    firstName: "Admin",
    lastName: "User",
    email: "<EMAIL>",
    password: "sara",
    isAdmin: true,
    profileImage: "/uploads/profile-images/admin.jpg",
  },
  {
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    password: "password123",
    isAdmin: false,
    profileImage: "/uploads/profile-images/user1.png",
  },
  {
    firstName: "Jane",
    lastName: "Smith",
    email: "<EMAIL>",
    password: "password123",
    isAdmin: false,
    profileImage: "/uploads/profile-images/user2.jpg",
  },
];

// Seed users
const seedUsers = async () => {
  try {
    // Clear existing users
    await User.deleteMany({});
    console.log("Deleted existing users");

    // Copy sample profile images
    await copyImages();

    // Create new users with hashed passwords
    const saltRounds = 10;
    const createdUsers = [];

    for (const user of users) {
      const hashedPassword = await bcrypt.hash(user.password, saltRounds);
      const newUser = new User({
        ...user,
        password: hashedPassword,
      });

      const savedUser = await newUser.save();
      createdUsers.push(savedUser);
    }

    console.log(`Created ${createdUsers.length} users with profile images`);

    // Disconnect from MongoDB
    mongoose.disconnect();
    console.log("MongoDB disconnected");
  } catch (error) {
    console.error("Error seeding users:", error);
    mongoose.disconnect();
  }
};

// Run the seed function
seedUsers();
