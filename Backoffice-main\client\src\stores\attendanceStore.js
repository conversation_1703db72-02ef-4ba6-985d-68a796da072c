import { defineStore } from 'pinia'
import { attendanceService } from '@/services/attendanceService'

export const useAttendanceStore = defineStore('attendance', {
  state: () => ({
    attendances: [],
    isLoading: false,
    error: null,
    presences: [],
    absents: [],
  }),
  actions: {
    async fetchAttendancesbyEmployeeId(id) {
      this.isLoading = true
      this.error = null
      try {
        this.attendances = await attendanceService.getAttendancesByEmployeeId(id)
      } catch (err) {
        this.error = err.message
        this.attendances = []
      } finally {
        this.isLoading = false
      }
    },
    async fetchTodayAttendances() {
      this.isLoading = true
      this.error = null
      try {
        // Récupérer la date du jour au format YYYY-MM-DD
        const today = new Date().toISOString().split('T')[0]

        // Appeler le service pour récupérer les présences du jour
        this.presences = await attendanceService.getTodayAttendances(today)

        // Calculer les absents (employés sans présence)
        const employeeStore = useEmployeeStore()
        const allEmployees = employeeStore.employees

        // Créer un ensemble des IDs des employés qui ont une présence
        const presentEmployeeIds = new Set(this.presences.map((presence) => presence.employeeId))

        // Filtrer les employés qui n'ont pas de présence
        this.absents = allEmployees.filter((employee) => !presentEmployeeIds.has(employee._id))

        return this.presences
      } catch (err) {
        this.error = err.message
        this.presences = []
        this.absents = []
        return []
      } finally {
        this.isLoading = false
      }
    },
  },
})
