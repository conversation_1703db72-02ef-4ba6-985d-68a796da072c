import api from '@/api'

export const employeeService = {
  async getAllEmployees() {
    try {
      const response = await api.get('/employees', { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(
        error.response?.data?.message || 'Erreur lors de la récupération des employés',
      )
    }
  },

  async getEmployeeById(id) {
    try {
      const response = await api.get(`/employees/${id}`, { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(
        error.response?.data?.message || "Erreur lors de la récupération de l'employé",
      )
    }
  },

  async addEmployee(employeeData) {
    try {
      const response = await api.post('/employees', employeeData, { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors de la création de l'employé")
    }
  },

  async updateEmployee(id, employeeData) {
    try {
      const response = await api.put(`/employees/${id}`, employeeData, {
        withCredentials: true,
      })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors de la mise à jour de l'employé")
    }
  },

  async deleteEmployee(id) {
    try {
      const response = await api.delete(`/employees/${id}`, { withCredentials: true })
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || "Erreur lors de la suppression de l'employé")
    }
  },
}
