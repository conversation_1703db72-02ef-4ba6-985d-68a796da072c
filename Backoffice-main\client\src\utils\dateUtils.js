/**
 * Formate une date en format français (DD/MM/YYYY)
 * @param {Date|String} date - La date à formater
 * @returns {String} - La date formatée
 */
export function formatDate(date) {
  if (!date) return ''
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  return d.toLocaleDateString('fr-FR')
}

/**
 * Formate une date et heure en format français (DD/MM/YYYY HH:MM)
 * @param {Date|String} date - La date à formater
 * @returns {String} - La date et heure formatées
 */
export function formatDateTime(date) {
  if (!date) return ''
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  return (
    d.toLocaleDateString('fr-FR') +
    ' ' +
    d.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })
  )
}

/**
 * Calcule la différence en jours entre deux dates
 * @param {Date|String} startDate - La date de début
 * @param {Date|String} endDate - La date de fin
 * @returns {Number} - Le nombre de jours entre les deux dates
 */
export function daysBetween(startDate, endDate) {
  if (!startDate || !endDate) return 0
  const start = new Date(startDate)
  const end = new Date(endDate)
  if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0

  // Ajuster pour ignorer l'heure
  const startDay = new Date(start.getFullYear(), start.getMonth(), start.getDate())
  const endDay = new Date(end.getFullYear(), end.getMonth(), end.getDate())

  // Calculer la différence en millisecondes et convertir en jours
  const diffTime = Math.abs(endDay - startDay)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  return diffDays + 1 // +1 pour inclure le jour de début
}
