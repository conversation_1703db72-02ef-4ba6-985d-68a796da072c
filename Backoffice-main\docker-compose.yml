version: "3.8"

services:
  mongo:
    image: mongo:6
    container_name: mongo
    restart: always
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db

  backend:
    build: ./backend
    container_name: backend
    restart: always
    ports:
      - "3000:3000"
    environment:
      - MONGODB_URI=mongodb://mongo:27017/backoffice
      - SESSION_SECRET=supersecret
    depends_on:
      - mongo

  frontend:
    build: ./client
    container_name: frontend
    restart: always
    ports:
      - "5173:5173"
    depends_on:
      - backend

volumes:
  mongo-data:
