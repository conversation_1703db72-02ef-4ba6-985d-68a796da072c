<template>
    <div class="mx-auto" style="max-width: 1800px;">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-2">
            <h1 class="text-2xl sm:text-3xl font-bold text-primary">Liste des demandes</h1>
            <div class="flex gap-2">
                <button class="btn btn-primary gap-2" @click="refreshDemands">
                    <i class="fas fa-sync-alt"></i>
                    <span>Actualiser</span>
                </button>

            </div>
        </div>

        <div class="bg-base-100 rounded-xl shadow p-2 sm:p-4 md:p-6 mb-4">
            <div class="flex flex-col sm:flex-row gap-4 mb-4">
                <div class="form-control w-full sm:w-64">
                    <label class="label">
                        <span class="label-text">Filtrer par statut</span>
                    </label>
                    <select v-model="statusFilter" class="select select-bordered w-full">
                        <option value="">Tous les statuts</option>
                        <option value="EN_ATTENTE">En attente</option>
                        <option value="ACCEPTE">Accepté</option>
                        <option value="REFUSE">Refusé</option>
                    </select>
                </div>
                <div class="form-control w-full sm:w-64">
                    <label class="label">
                        <span class="label-text">Rechercher</span>
                    </label>
                    <input type="text" v-model="searchTerm" placeholder="Rechercher..."
                        class="input input-bordered w-full" />
                </div>
            </div>
        </div>

        <div class="overflow-x-auto bg-base-100 rounded-xl shadow p-2 sm:p-4 md:p-6">
            <DataTable :headers="demandHeaders" :data="paginatedDemands" :showActions="true" @onEdit="handleEdit"
                @onDelete="handleDelete" class="min-w-[600px] text-xs sm:text-sm md:text-base">
                <template #cell-status="{ row }">
                    <span class="badge" :class="getStatusBadgeClass(row.status)">
                        {{ getStatusLabel(row.status) }}
                    </span>
                </template>
                <template #cell-requestedAt="{ row }">
                    {{ formatDate(row.requestedAt) }}
                </template>
                <template #cell-startDate="{ row }">
                    {{ formatDate(row.startDate) }}
                </template>
                <template #cell-endDate="{ row }">
                    {{ formatDate(row.endDate) }}
                </template>
                <template #actions="{ row }">
                    <div class="flex gap-2">
                        <!-- Boutons d'acceptation et de rejet (uniquement pour les demandes en attente) -->
                        <div v-if="row.status === 'EN_ATTENTE'" class="flex gap-1">
                            <button class="btn btn-xs sm:btn-sm btn-outline btn-success"
                                @click.stop="handleAccept(row._id)" title="Accepter la demande">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-xs sm:btn-sm btn-outline btn-error"
                                @click.stop="handleReject(row._id)" title="Rejeter la demande">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <!-- Boutons d'édition et de suppression -->
                        <button class="btn btn-xs sm:btn-sm btn-outline btn-info" @click.stop="handleEdit(row._id)"
                            title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-xs sm:btn-sm btn-outline btn-error" @click.stop="handleDelete(row._id)"
                            title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </template>
            </DataTable>
            <div v-if="filteredDemands.length === 0" class="flex flex-col items-center py-8">
                <i class="fas fa-file-alt text-4xl mb-4 text-gray-400"></i>
                <p class="text-gray-500">Aucune demande trouvée</p>
            </div>
            <Pagination v-if="totalPages > 1" :currentPage="currentPage" :totalPages="totalPages"
                @update:currentPage="currentPage = $event" />
        </div>
    </div>
</template>

<script>
import DataTable from '@/components/Table.vue';
import Pagination from '@/components/Pagination.vue';
import { useDemandStore } from '@/stores/demandStore';
import { useEmployeeStore } from '@/stores/employeeStore';
import { push } from 'notivue';

export default {
    name: 'DemandView',
    components: { DataTable, Pagination },
    data() {
        return {
            demandStore: useDemandStore(),
            employeeStore: useEmployeeStore(),
            statusFilter: '',
            searchTerm: '',
            currentPage: 1,
            demandsPerPage: 10,
            demandHeaders: [
                { label: 'Type', value: 'type' },
                { label: 'Salarié', value: 'employeeName' },
                { label: 'Date de dépôt', value: 'requestedAt' },
                { label: 'Statut', value: 'status' },
                { label: 'Début', value: 'startDate' },
                { label: 'Fin', value: 'endDate' },
            ]
        };
    },
    computed: {
        demands() {
            return this.demandStore.demands;
        },
        employees() {
            return this.employeeStore.employees;
        },
        filteredDemands() {
            let filtered = this.demands;

            // Filtrer par statut
            if (this.statusFilter) {
                filtered = filtered.filter(d => d.status === this.statusFilter);
            }

            // Filtrer par terme de recherche
            if (this.searchTerm) {
                const term = this.searchTerm.toLowerCase();
                filtered = filtered.filter(d => {
                    // Rechercher dans le type
                    if (d.type && d.type.toLowerCase().includes(term)) return true;

                    // Rechercher dans le nom de l'employé
                    const employee = this.findEmployee(d.employeeId || d.employee);
                    if (employee) {
                        const fullName = `${employee.firstName} ${employee.lastName}`.toLowerCase();
                        if (fullName.includes(term)) return true;
                    }

                    return false;
                });
            }

            return filtered;
        },
        demandsWithEmployeeNames() {
            return this.filteredDemands.map(demand => {
                const employee = this.findEmployee(demand.employeeId || demand.employee);
                return {
                    ...demand,
                    employeeName: employee
                        ? `${employee.firstName} ${employee.lastName}`
                        : 'Employé inconnu'
                };
            });
        },
        // Nouvelle computed property pour la pagination
        paginatedDemands() {
            const startIndex = (this.currentPage - 1) * this.demandsPerPage;
            const endIndex = startIndex + this.demandsPerPage;
            return this.demandsWithEmployeeNames.slice(startIndex, endIndex);
        },
        // Nouvelle computed property pour calculer le nombre total de pages
        totalPages() {
            return Math.ceil(this.demandsWithEmployeeNames.length / this.demandsPerPage);
        }
    },
    async mounted() {
        try {
            await this.demandStore.fetchDemands();
            await this.employeeStore.fetchAllEmployees();
        } catch (error) {
            push.error({
                title: 'Erreur',
                message: 'Impossible de récupérer les demandes'
            });
        }
    },
    methods: {
        findEmployee(employeeId) {
            if (!employeeId) return null;

            // Si employeeId est un objet avec _id
            if (typeof employeeId === 'object' && employeeId._id) {
                employeeId = employeeId._id;
            }

            return this.employees.find(e => e._id === employeeId);
        },
        formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleDateString('fr-FR');
        },
        getStatusLabel(status) {
            switch (status) {
                case 'EN_ATTENTE': return 'En attente';
                case 'ACCEPTE': return 'Accepté';
                case 'REFUSE': return 'Refusé';
                default: return status;
            }
        },
        getStatusBadgeClass(status) {
            switch (status) {
                case 'EN_ATTENTE': return 'badge-warning';
                case 'ACCEPTE': return 'badge-success';
                case 'REFUSE': return 'badge-error';
                default: return 'badge-neutral';
            }
        },
        handleEdit(id) {
            this.$router.push({ name: 'edit-demand', params: { id } });
        },
        handleDelete(id) {
            if (confirm('Voulez-vous vraiment supprimer cette demande ?')) {
                this.demandStore.deleteDemand(id)
                    .then(() => {
                        push.success({
                            title: 'Succès',
                            message: 'Demande supprimée avec succès'
                        });
                    })
                    .catch(error => {
                        push.error({
                            title: 'Erreur',
                            message: error.message || 'Erreur lors de la suppression'
                        });
                    });
            }
        },
        refreshDemands() {
            this.demandStore.fetchDemands()
                .then(() => {
                    push.success({
                        title: 'Succès',
                        message: 'Liste des demandes actualisée'
                    });
                })
                .catch(error => {
                    push.error({
                        title: 'Erreur',
                        message: error.message || 'Erreur lors de l\'actualisation'
                    });
                });
        },
        // Nouvelle méthode pour accepter une demande
        handleAccept(id) {
            if (confirm('Voulez-vous vraiment accepter cette demande ?')) {
                this.demandStore.updateDemand(id, { status: 'ACCEPTE' })
                    .then(() => {
                        push.success({
                            title: 'Succès',
                            message: 'Demande acceptée avec succès'
                        });
                    })
                    .catch(error => {
                        push.error({
                            title: 'Erreur',
                            message: error.message || 'Erreur lors de l\'acceptation de la demande'
                        });
                    });
            }
        },

        // Nouvelle méthode pour rejeter une demande
        handleReject(id) {
            if (confirm('Voulez-vous vraiment rejeter cette demande ?')) {
                this.demandStore.updateDemand(id, { status: 'REFUSE' })
                    .then(() => {
                        push.success({
                            title: 'Succès',
                            message: 'Demande rejetée avec succès'
                        });
                    })
                    .catch(error => {
                        push.error({
                            title: 'Erreur',
                            message: error.message || 'Erreur lors du rejet de la demande'
                        });
                    });
            }
        }
    }
};
</script>

<style scoped>
.badge-warning {
    background-color: #f59e0b;
    color: white;
}

.badge-success {
    background-color: #10b981;
    color: white;
}

.badge-error {
    background-color: #ef4444;
    color: white;
}

.badge-neutral {
    background-color: #6b7280;
    color: white;
}
</style>
