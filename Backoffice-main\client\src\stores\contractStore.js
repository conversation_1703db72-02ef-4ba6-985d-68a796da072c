import { defineStore } from 'pinia'
import { contractService } from '@/services/contractService'

export const useContractStore = defineStore('contractStore', {
  state: () => ({
    contracts: [],
    isLoading: false,
    error: null,
  }),
  actions: {
    async fetchContracts() {
      this.isLoading = true
      this.error = null
      try {
        const contracts = await contractService.getAllContracts()
        this.contracts = contracts
      } catch (err) {
        this.error = err.message || 'Erreur lors du chargement des contrats'
      } finally {
        this.isLoading = false
      }
    },
    async fetchContractsByEmployeeId(employeeId) {
      this.isLoading = true
      this.error = null
      try {
        const contracts = await contractService.getContractsByEmployeeId(employeeId)
        this.contracts = contracts
      } catch (err) {
        this.error = err.message || "Erreur lors du chargement des contrats de l'employé"
      } finally {
        this.isLoading = false
      }
    },
    async addContract(contractData) {
      this.isLoading = true
      this.error = null
      try {
        const response = await contractService.addContract(contractData)
        console.log('Réponse du serveur après ajout:', response)

        // Vérifier si la réponse contient le contrat
        if (response && response.contract) {
          // Ajouter le nouveau contrat à la liste des contrats
          this.contracts.push(response.contract)
          console.log('Contrat ajouté au store:', response.contract)
          console.log('Liste des contrats mise à jour:', this.contracts)
        } else {
          console.warn('La réponse ne contient pas de contrat:', response)
        }
        return response
      } catch (err) {
        this.error = err.message || "Erreur lors de l'ajout du contrat"
        throw err
      } finally {
        this.isLoading = false
      }
    },
    async deleteContract(id) {
      try {
        await contractService.deleteContract(id)
        this.contracts = this.contracts.filter((c) => c._id !== id)
      } catch (err) {
        this.error = err.message || 'Erreur lors de la suppression du contrat'
      }
    },
    async updateContract(id, updateData) {
      try {
        const updated = await contractService.updateContract(id, updateData)
        const idx = this.contracts.findIndex((c) => c._id === id)
        if (idx !== -1) this.contracts[idx] = updated
      } catch (err) {
        this.error = err.message || 'Erreur lors de la modification du contrat'
      }
    },
    async getContractById(id) {
      try {
        return await contractService.getContractById(id)
      } catch (err) {
        this.error = err.message || 'Erreur lors de la récupération du contrat'
        throw err
      }
    },
  },
})
