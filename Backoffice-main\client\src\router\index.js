import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import LoginView from '../views/LoginView.vue'
import { useAuthStore } from '@/stores/authStore'
import AddUserView from '@/views/AddUserView.vue'
import UsersListView from '@/views/UsersListView.vue'
import UserProfileView from '@/views/UserProfileView.vue'
import DepartmentListView from '@/views/DepartmentListView.vue'
import AddDepartmentView from '@/views/AddDepartmentView.vue'
import AttendanceView from '@/views/AttendanceView.vue'
import HomePage from '@/views/HomePage.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'HomePage',
      component: HomePage,
      meta: { hideNavbar: true },
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: {
        hideNavbar: true,
        guest: true,
      },
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: HomeView,
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: '/users',
      name: 'users-list',
      component: UsersListView,
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: '/users/add',
      name: 'add-user',
      component: AddUserView,
      meta: {
        requiresAuth: true,
        isAdmin: true,
      },
    },
    {
      path: '/users/:id',
      name: 'user-profile',
      component: UserProfileView,
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: '/users/:id/edit',
      name: 'edit-user',
      component: () => import('@/views/EditUserView.vue'),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: '/employees',
      name: 'employees-list',
      component: () => import('@/views/EmployeeListView.vue'),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: '/employees/add',
      name: 'add-employee',
      component: () => import('@/views/AddEmployeeView.vue'),
      meta: {
        requiresAuth: true,
        isAdmin: true,
      },
    },
    {
      path: '/employees/:id/edit',
      name: 'EditEmployee',
      component: () => import('@/views/EditEmployeeView.vue'),
      meta: {
        requiresAuth: true,
        isAdmin: true,
      },
    },
    {
      path: '/departments',
      name: 'departments',
      component: DepartmentListView,
    },
    {
      path: '/departments/add',
      name: 'add-department',
      component: AddDepartmentView,
    },
    {
      path: '/attendances',
      name: 'présence',
      component: AttendanceView,
    },
    {
      path: '/dashbord',
      name: 'tableau de bord',
      component: HomeView,
    },

    // Dans src/router/index.js
    {
      path: '/employees/:id',
      name: 'EmployeeProfile',
      component: () => import('@/views/EmployeeProfileView.vue'),
    },
    {
      path: '/demands',
      name: 'demandes',
      component: () => import('@/views/DemandView.vue'),
    },
    {
      path: '/contracts/add/:employeeId',
      name: 'AddContract',
      component: () => import('@/views/AddContractView.vue'),
    },
  ],
})

// Navigation guard
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Vérifier l'authentification au chargement
  if (!authStore.isAuthenticated) {
    await authStore.checkAuth()
  }

  // Rediriger les utilisateurs connectés qui vont sur une page guest vers le dashboard
  if (to.matched.some((record) => record.meta.guest) && authStore.isAuthenticated) {
    next({ name: 'dashboard' })
    return
  }

  // Rediriger les non-authentifiés qui vont sur une page protégée vers la page d'accueil
  if (to.matched.some((record) => record.meta.requiresAuth)) {
    if (!authStore.isAuthenticated) {
      next({ name: 'HomePage' })
      return
    }
  }

  next()
})

export default router
