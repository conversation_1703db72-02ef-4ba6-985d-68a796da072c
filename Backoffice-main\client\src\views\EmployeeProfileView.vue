<template>
    <div class="container mx-auto">
        <!-- Ligne du haut : bouton retour + cartes -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
            <button @click="$router.back()" class="btn btn-outline btn-sm self-start md:self-center">
                <i class="fas fa-arrow-left mr-2"></i> Retour
            </button>
            <div class="flex flex-col md:flex-row gap-4">
                <!-- Solde de pointage -->
                <div class="card w-64 bg-base-100 shadow border-l-4 border-success">
                    <div class="card-body p-4 flex flex-row items-center gap-4">
                        <div class="bg-success/10 rounded-full p-3">
                            <i class="fas fa-clock text-success text-2xl"></i>
                        </div>
                        <div>
                            <div class="text-xs text-gray-500 font-semibold uppercase">Solde de pointage</div>
                            <div class="text-xl font-bold text-success">
                                {{ totalHoursWorked }}<span class="text-xs font-normal text-gray-500 ml-1">Heures</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Solde de congés -->
                <div class="card w-64 bg-base-100 shadow border-l-4 border-info">
                    <div class="card-body p-4 flex flex-row items-center gap-4">
                        <div class="bg-info/10 rounded-full p-3">
                            <i class="fas fa-calendar-check text-info text-2xl"></i>
                        </div>
                        <div>
                            <div class="text-xs text-gray-500 font-semibold uppercase">Solde de congés</div>
                            <div class="text-xl font-bold text-info">
                                {{ employee?.leaveBalance || 8 }}
                                <span class="text-xs font-normal text-gray-500 ml-1">Jours</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Bloc principal -->
        <div class="flex flex-col md:flex-row gap-8  mx-auto bg-base-100 rounded-xl shadow p-8 mt-8">
            <!-- Colonne gauche : Infos employé -->
            <div class="flex-1 min-w-[320px] max-w-md bg-white rounded-lg p-6 shadow flex flex-col items-start">
                <!-- Remplacer la div d'image par le composant ProfileImage -->
                <ProfileImage v-if="employee && employee._id" :userId="employee._id"
                    :profileImage="employee.profileImage || ''" :editable="false" class="mb-5" />
                <h2 class="text-xl font-bold mb-2">Profil de {{ employee?.firstName }} {{ employee?.lastName }}</h2>
                <Table :headers="employeeInfoHeaders" :data="employeeInfoData" />
                <div class="w-full mt-4">
                    <h3 class="font-semibold mb-2">Liste des demandes</h3>
                    <Table :headers="demandHeaders" :data="demandes">
                        <template #cell-requestedAt="{ row }">
                            {{ formatDate(row.requestedAt) }}
                        </template>
                        <template #cell-actions="{ row }">
                            <button @click="downloadDemand(row)" class="btn btn-xs btn-outline btn-primary">
                                <i class="fas fa-download mr-1"></i> Télécharger
                            </button>
                        </template>
                    </Table>
                </div>
            </div>
            <!-- Colonne droite : Contrats + Calendrier -->
            <div class="flex-1 flex flex-col gap-8">
                <div>
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="font-semibold">Liste des contrats</h3>
                        <button @click="redirectToAddContract" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus mr-1"></i> Ajouter un contrat
                        </button>
                    </div>
                    <Table :headers="contractHeaders" :data="contracts">
                        <template #cell-startDate="{ row }">
                            {{ formatDate(row.startDate) }}
                        </template>
                        <template #cell-endDate="{ row }">
                            {{ formatDate(row.endDate) }}
                        </template>
                    </Table>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">Historique du pointage</h3>
                    <vue-cal
                        style="height: 600px; width: 100%; background: #fff; border-radius: 10px; box-shadow: 0 2px 8px #e0e0e0;"
                        :events="attendanceEvents" :time="false" default-view="week" locale="fr"
                        events-on-month-view="true" :disable-views="['week', 'day']" :editable-events="false" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import VueCal from 'vue-cal';
// For vue-cal v4.8.1, this is the correct import
import 'vue-cal/dist/vuecal.css';
import Table from '@/components/Table.vue';
import ProfileImage from '@/components/ProfileImage.vue'; // Importer le composant ProfileImage
import { useAttendanceStore } from '@/stores/attendanceStore';
import { useDemandStore } from '@/stores/demandStore';
import { useContractStore } from '@/stores/contractStore';
import { storeToRefs } from 'pinia';
import { useEmployeeStore } from '@/stores/employeeStore';
import { push } from 'notivue';
import apiClient from '@/api';
import { saveAs } from 'file-saver';
// Supprimons l'import problématique et définissons la fonction localement

export default {
    name: 'EmployeeProfileView',
    components: { VueCal, Table, ProfileImage }, // Ajouter ProfileImage aux composants
    data() {
        return {
            employee: null,
            attendanceEvents: [],
            totalHoursWorked: 0,
            employeeInfoHeaders: [
                { label: 'Champ', value: 'field' },
                { label: 'Valeur', value: 'value' }
            ],
            demandHeaders: [
                { label: 'Type', value: 'type' },
                { label: 'Date dépôt', value: 'requestedAt' },
                { label: 'Statut', value: 'status' },
                { label: 'Actions', value: 'actions' }
            ],
            contractHeaders: [
                { label: 'Type', value: 'type' },
                { label: 'Date début', value: 'startDate' },
                { label: 'Date fin', value: 'endDate' },
                { label: 'Statut', value: 'status' }
            ]
        };
    },
    computed: {
        employeeInfoData() {
            if (!this.employee) return [];
            return [
                { field: 'Email', value: this.employee.email },
                { field: 'Téléphone', value: this.employee.phone },
                { field: 'Département', value: this.employee.department },
                { field: 'Adresse', value: this.employee.address },
                { field: 'Date de naissance', value: this.formatDate(this.employee.birthDate) },
                { field: "Date d'embauche", value: this.formatDate(this.employee.hireDate) },
                { field: 'Statut', value: this.employee.status }
            ];
        },
        // Ajout des références aux stores avec storeToRefs
        ...storeToRefs(useAttendanceStore()),
        ...storeToRefs(useDemandStore()),
        ...storeToRefs(useContractStore()),
        // Ajout de la propriété demandes manquante
        demandes() {
            const demandStore = useDemandStore();
            return demandStore.demands.filter(d =>
                d.employeeId === this.$route.params.id ||
                d.employee === this.$route.params.id
            );
        },
        // Modifier la propriété contracts pour filtrer par employeeId
        contracts() {
            const contractStore = useContractStore();
            const employeeId = this.$route.params.id;

            // Filtrer les contrats pour ne garder que ceux de l'employé actuel
            const employeeContracts = contractStore.contracts.filter(contract =>
                contract.employeeId === employeeId ||
                (contract.employee && (
                    contract.employee === employeeId ||
                    contract.employee._id === employeeId
                ))
            );

            // Trier par date de création décroissante (le plus récent en premier)
            const sortedContracts = [...employeeContracts].sort((a, b) => {
                const dateA = new Date(a.createdAt || a.startDate);
                const dateB = new Date(b.createdAt || b.startDate);
                return dateB - dateA;
            });

            // Retourner uniquement le contrat le plus récent, ou un tableau vide s'il n'y a pas de contrat
            return sortedContracts.length > 0 ? [sortedContracts[0]] : [];
        }
    },
    async mounted() {
        await this.loadEmployeeData();
    },
    methods: {
        async loadEmployeeData() {
            const id = this.$route.params.id;
            // Fetch employee info from the store
            const employeeStore = useEmployeeStore();
            this.employee = await employeeStore.getEmployeeById(id);

            // Attendance
            const attendanceStore = useAttendanceStore();
            await attendanceStore.fetchAttendancesbyEmployeeId(id);
            const { attendances } = storeToRefs(attendanceStore);

            // Contracts - Charger spécifiquement les contrats de cet employé
            const contractStore = useContractStore();
            await contractStore.fetchContractsByEmployeeId(id);
            console.log('Contrats chargés pour l\'employé', id, ':', contractStore.contracts);

            // Demands
            const demandStore = useDemandStore();
            await demandStore.fetchDemandsByEmployeeId(id);

            // Attendance events
            const filteredAttendances = attendances.value.filter(a => a.employeeId == id);
            this.attendanceEvents = filteredAttendances
                .filter(a => a.date && !isNaN(new Date(a.date)))
                .map(a => {
                    const dateISO = new Date(a.date).toISOString().slice(0, 10);
                    const status = a.status?.toLowerCase() || 'absent';
                    let color, bgColor, borderColor;
                    switch (status) {
                        case 'present':
                            color = '#4CAF50'; bgColor = 'rgba(76, 175, 80, 0.08)'; borderColor = '#4CAF50'; break;
                        case 'absent':
                            color = '#F44336'; bgColor = 'rgba(244, 67, 54, 0.08)'; borderColor = '#F44336'; break;
                        case 'retard':
                            color = '#FFC107'; bgColor = 'rgba(255, 193, 7, 0.08)'; borderColor = '#FFC107'; break;
                        default:
                            color = '#757575'; bgColor = 'rgba(117, 117, 117, 0.08)'; borderColor = '#757575';
                    }
                    return {
                        start: dateISO,
                        end: dateISO,
                        content: `<div style="display:flex;align-items:center;gap:8px;">
                            <span style="display:inline-block;width:12px;height:12px;border-radius:50%;background:${color};"></span>
                            <div style="display:flex;flex-direction:column;align-items:flex-start;">
                                <span style="font-weight:bold;color:${color};font-size:1em;">${status.charAt(0).toUpperCase() + status.slice(1)}</span>
                                <span style="font-size:0.95em;color:#222;">${a.hoursWorked ? a.hoursWorked + 'h travaillées' : ''}</span>
                            </div>
                        </div>`,
                        class: status,
                        background: true,
                        split: 1,
                        style: {
                            backgroundColor: bgColor,
                            borderLeft: `4px solid ${borderColor}`,
                            borderRadius: '6px',
                            padding: '8px',
                            minHeight: '38px',
                            display: 'flex',
                            alignItems: 'center',
                            margin: '2px 0'
                        }
                    };
                });

            this.totalHoursWorked = filteredAttendances.reduce((sum, a) => sum + (a.hoursWorked || 0), 0);
        },
        /**
         * Formate une date en format français (DD/MM/YYYY)
         * @param {Date|String} date - La date à formater
         * @returns {String} - La date formatée
         */
        formatDate(date) {
            if (!date) return '';
            const d = new Date(date);
            if (isNaN(d.getTime())) return '';
            return d.toLocaleDateString('fr-FR');
        },
        onEventClick(event, e) {
            // Vous pouvez ajouter une action lors du clic sur un événement
            console.log(event);
        },
        onEventCreate(event, e) {
            // Vous pouvez ajouter une action lors de la création d'un événement
            return event;
        },
        redirectToAddContract() {
            try {
                if (!this.employee) {
                    throw new Error("Les informations de l'employé ne sont pas disponibles");
                }

                this.$router.push({
                    name: 'AddContract',
                    params: { employeeId: this.$route.params.id },
                    query: {
                        employeeName: `${this.employee.firstName} ${this.employee.lastName}`
                    }
                });
            } catch (error) {
                console.error("Erreur lors de la redirection:", error);
                push.error({
                    title: 'Erreur',
                    message: "Impossible d'accéder à la page d'ajout de contrat"
                });
            }
        },
        async downloadDemand(demand) {
            try {
                push.info({
                    title: 'Génération en cours',
                    message: 'Génération du PDF en cours...'
                });

                // Ajoutons des logs pour déboguer
                console.log('Demande à télécharger:', demand);

                const response = await apiClient.post(`/pdf/demand/${demand._id}`, {}, {
                    responseType: 'blob'
                });

                console.log('Réponse reçue:', response);

                const fileName = `demande_${demand.type}_${this.employee.lastName}_${this.formatDate(demand.requestedAt).replace(/\//g, '-')}.pdf`;

                saveAs(new Blob([response.data]), fileName);

                push.success({
                    title: 'Succès',
                    message: 'Demande téléchargée avec succès'
                });
            } catch (error) {
                console.error('Erreur lors du téléchargement:', error);
                // Affichons plus de détails sur l'erreur
                if (error.response) {
                    console.error('Détails de l\'erreur:', {
                        status: error.response.status,
                        data: error.response.data
                    });
                }
                push.error({
                    title: 'Erreur',
                    message: 'Impossible de télécharger la demande'
                });
            }
        }
    },
    beforeRouteEnter(to, from, next) {
        next(vm => {
            // Si on revient de la page d'ajout de contrat, rafraîchir les données
            if (from.name === 'AddContract') {
                vm.loadEmployeeData();
            }
        });
    }
};
</script>

<style scoped>
.vuecal__event.present {
    background-color: rgba(76, 175, 80, 0.1) !important;
    border-left: 4px solid #4CAF50 !important;
    color: #4CAF50;
}

.vuecal__event.absent {
    background-color: rgba(244, 67, 54, 0.1) !important;
    border-left: 4px solid #F44336 !important;
    color: #F44336;
}

.vuecal__event.retard {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-left: 4px solid #FFC107 !important;
    color: #FFC107;
}

.vuecal__event {
    border-radius: 6px !important;
    padding: 8px !important;
    margin: 2px 0 !important;
    min-height: 38px !important;
    display: flex !important;
    align-items: center !important;
    box-shadow: none !important;
}

.vuecal__event-content {
    width: 100%;
    display: flex;
    align-items: center;
}

.vuecal__event i {
    font-size: 0.8em;
    margin-right: 4px;
}

.vuecal__weekdays-headings {
    font-weight: bold;
    border-bottom: 1px solid #eee;
}

.vuecal__heading {
    font-weight: bold;
}

.vuecal__cell--today {
    background-color: rgba(66, 133, 244, 0.05);
}

.vuecal__cell--selected {
    background-color: rgba(66, 133, 244, 0.1);
}

.vuecal__title-bar {
    background-color: #f5f5f5;
    padding: 8px;
}

.vuecal__menu {
    background-color: #f5f5f5;
}

/* Styles supplémentaires pour correspondre à l'image */
.vuecal__view-week {
    border-top: 1px solid #eee;
}

.vuecal__cell {
    border: 1px solid #eee;
}

.vuecal__cell-content {
    padding: 4px;
}

.vuecal__cell-events-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    padding: 0 4px;
    border-radius: 10px;
    background-color: rgba(66, 133, 244, 0.9);
    color: #fff;
    font-size: 0.8em;
    font-weight: bold;
}

/* Styles pour les jours de la semaine */
.vuecal__weekdays-headings .vuecal__heading {
    text-transform: lowercase;
    font-size: 0.9em;
    color: #666;
}

/* Style pour le titre de l'événement */
.vuecal__event-title {
    font-weight: bold;
    margin-bottom: 2px;
}

/* Style pour le contenu de l'événement */
.vuecal__event-content {
    font-size: 0.85em;
}
</style>
