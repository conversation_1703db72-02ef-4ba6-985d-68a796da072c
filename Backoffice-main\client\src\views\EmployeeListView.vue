<template>
  <div class=" mx-auto" style="max-width: 1800px;">
    <Loading v-if="isLoading" />
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">Liste des employés</h1>
      <RouterLink to="/employees/add" v-if="isAdmin" class="btn btn-primary gap-2">
        <span>Ajouter un employé</span>
        <i class="fas fa-user-plus"></i>
      </RouterLink>
    </div>
    <SearchBar v-model="searchTerm" />
    <div class="overflow-x-auto bg-base-100 rounded-xl shadow p-6">
      <div v-if="!isLoading && employees.length === 0" class="flex flex-col items-center py-8">
        <i class="fas fa-users text-4xl mb-4 text-gray-400"></i>
        <p class="text-gray-500">Aucun employé trouvé</p>
      </div>
      <DataTable v-else-if="!isLoading" :headers="employeeHeaders" :data="paginatedEmployees" :showActions="isAdmin"
        @onEdit="handleEdit" @onDelete="handleDelete">
        <template #cell-avatar="{ row }">
          <div class="avatar">
            <div class="w-10 h-10 rounded-full ring ring-primary ring-offset-base-100 ring-offset-1">
              <img :src="getEmployeeAvatar(row)" :alt="`${row.firstName} ${row.lastName}`" class="object-cover"
                @error="handleImageError($event, row)" />
            </div>
          </div>
        </template>
        <template #cell-fullName="{ row }">
          <span class="cursor-pointer text-success hover:underline" @click="goToEmployeeProfile(row._id)">
            {{ row.fullName }}
          </span>
        </template>
        <template #actions="{ row }">
          <div class="flex gap-2">
            <button @click.stop="handleEdit(row._id)" class="btn btn-sm btn-outline btn-info">
              <i class="fas fa-edit"></i>
            </button>
            <button @click.stop="handleDelete(row._id)" class="btn btn-sm btn-outline btn-error">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </template>
      </DataTable>
      <Pagination v-if="totalPages > 1" :currentPage="currentPage" :totalPages="totalPages"
        @update:currentPage="currentPage = $event" />
    </div>
  </div>
</template>

<script>
import { useAuthStore } from '@/stores/authStore';
import { useEmployeeStore } from '@/stores/employeeStore';
import Loading from '@/components/Loading.vue';
import Pagination from '@/components/Pagination.vue';
import { push } from 'notivue';
import { mapState } from 'pinia';
import DataTable from '@/components/Table.vue';
import SearchBar from '@/components/SearchBar.vue';

export default {
  name: 'EmployeeListView',
  components: { Loading, Pagination, DataTable, SearchBar },
  data() {
    return {
      currentPage: 1,
      employeesPerPage: 8,
      searchTerm: "",
      employeeStore: useEmployeeStore(),
      employeeHeaders: [
        { label: 'Avatar', value: 'avatar' },
        { label: 'Nom', value: 'fullName' },
        { label: 'Email', value: 'email' },
        { label: 'Département', value: 'department' },
        { label: 'Genre', value: 'gender' },
        { label: 'Date de naissance', value: 'Date' },
        { label: 'Téléphone', value: 'phone' },
        { label: 'Adresse', value: 'address' },
        { label: "Date d'embauche", value: 'hireDate' },
        { label: 'Statut', value: 'status' }
      ]
    };
  },
  computed: {
    ...mapState(useAuthStore, ['user']),
    currentUser() { return this.user; },
    isAdmin() { return this.currentUser?.isAdmin; },
    employees() { return this.employeeStore.employees; },
    isLoading() { return this.employeeStore.isLoading; },
    filteredEmployees() {
      if (!this.searchTerm) return this.employees;
      const term = this.searchTerm.toLowerCase();
      return this.employees.filter(e =>
        (e.firstName && e.firstName.toLowerCase().includes(term)) ||
        (e.lastName && e.lastName.toLowerCase().includes(term))
      );
    },
    paginatedEmployees() {
      const start = ((this.currentPage || 1) - 1) * (this.employeesPerPage || 8);
      const end = start + (this.employeesPerPage || 8);
      return this.filteredEmployees.slice(start, end).map(e => ({
        ...e,
        fullName: `${e.firstName} ${e.lastName}`
      }));
    },
    totalPages() {
      return Math.ceil(this.filteredEmployees.length / (this.employeesPerPage || 8));
    }
  },
  async mounted() {
    try {
      await this.employeeStore.fetchAllEmployees();
    } catch (error) {
      push.error({ title: 'Erreur', message: 'Impossible de récupérer la liste des employés' });
    }
  },
  methods: {
    getEmployeeAvatar(employee) {
      if (employee.profileImage) {
        if (employee.profileImage.startsWith('http')) {
          return employee.profileImage;
        }
        return `${import.meta.env.VITE_BASE_URL || ''}${employee.profileImage}`;
      }
      return `https://ui-avatars.com/api/?name=${employee.firstName}+${employee.lastName}&background=random`;
    },
    handleImageError(event, employee) {
      // En cas d'erreur de chargement d'image, utiliser un avatar généré
      event.target.src = `https://ui-avatars.com/api/?name=${employee.firstName}+${employee.lastName}&background=random`;
    },
    handleEdit(id) {
      this.$router.push({ name: 'EditEmployee', params: { id } });
    },
    async handleDelete(id) {
      if (confirm("Voulez-vous vraiment supprimer cet employé ?")) {
        try {
          await this.employeeStore.deleteEmployee(id);
          push.success({ title: 'Succès', message: 'Employé supprimé avec succès' });
          await this.employeeStore.fetchAllEmployees();
        } catch (error) {
          push.error({ title: 'Erreur', message: error.message || "Une erreur est survenue lors de la suppression" });
        }
      }
    },
    goToEmployeeProfile(id) {
      try {
        this.$router.push({ name: 'EmployeeProfile', params: { id } });
      } catch (error) {
        console.error('Erreur de navigation:', error);
        push.error({
          title: 'Erreur',
          message: 'Impossible d\'accéder au profil de l\'employé'
        });
      }
    }
  }
};
</script>

<style scoped>
.avatar {
  display: inline-flex;
  position: relative;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
