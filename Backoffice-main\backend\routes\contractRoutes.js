const express = require("express");
const router = express.Router();
const contractController = require("../controllers/ContractController");
const contractService = require("../services/contractService");
const authMiddleware = require("../middleware/authMiddleware");

// Appliquer l'authentification à toutes les routes
router.use(authMiddleware);

// Route pour récupérer tous les contrats ou filtrer par employeeId
router.get("/", async (req, res) => {
  try {
    if (req.query.employeeId) {
      // Si un employeeId est fourni, récupérer les contrats de cet employé
      console.log(
        "Route - Récupération des contrats pour l'employé:",
        req.query.employeeId
      );
      const contracts = await contractService.getContractsByEmployeeId(
        req.query.employeeId
      );
      console.log("Route - Contrats trouvés:", contracts.length);
      res.status(200).json(contracts);
    } else {
      // Sinon, récupérer tous les contrats
      const contracts = await contractService.getAllContracts();
      res.status(200).json(contracts);
    }
  } catch (error) {
    console.error(
      "Route - Erreur lors de la récupération des contrats:",
      error
    );
    res.status(500).json({
      message: "Erreur lors de la récupération des contrats",
      error: error.message,
    });
  }
});

router.post("/", contractController.createContract);
router.get("/:id", contractController.getContractById);
router.put("/:id", contractController.updateContract);
router.delete("/:id", contractController.deleteContract);

module.exports = router;
