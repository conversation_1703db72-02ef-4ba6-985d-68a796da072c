# TABLE DES MATIÈRES

REMERCIEMENTS ................................................................................................................................ 2
RÉSUMÉ ............................................................................................................................................. 3
MOTS-CLÉS ........................................................................................................................................ 3
ABSTRACT .......................................................................................................................................... 4

INTRODUCTION GÉNÉRALE ................................................................................................................. 5
- Contexte du projet
- Problématique générale
- Approche méthodologique
- Structure du rapport

CHAPITRE I : CONTEXTE ET ANALYSE DES BESOINS ............................................................................... 7
1. CONTEXTE DU PROJET ...................................................................................................................................................... 8
   1.1 Présentation du domaine de la gestion RH ......................................................................................................... 8
   1.2 Enjeux actuels des systèmes d'information RH .................................................................................................. 9
2. ANALYSE DE L'EXISTANT .................................................................................................................................................. 10
   2.1 Processus RH traditionnels ................................................................................................................................ 10
   2.2 Limites des approches actuelles ........................................................................................................................ 11
   2.3 Étude comparative des solutions du marché .................................................................................................... 12
3. EXPRESSION DES BESOINS ............................................................................................................................................... 13
   3.1 Besoins fonctionnels ......................................................................................................................................... 13
   3.2 Besoins non-fonctionnels .................................................................................................................................. 15
   3.3 Contraintes techniques et organisationnelles .................................................................................................. 16
CONCLUSION ...................................................................................................................................................................... 17

CHAPITRE II : CONCEPTION DE LA SOLUTION ......................................................................................18
1. ARCHITECTURE GLOBALE ................................................................................................................................................. 19
   1.1 Vue d'ensemble du système .............................................................................................................................. 19
   1.2 Architecture à deux applications : Portail et Backoffice ................................................................................... 20
   1.3 Flux de données et interactions ........................................................................................................................ 21
2. MODÉLISATION UML ...................................................................................................................................................... 22
   2.1 Diagramme des cas d'utilisation ....................................................................................................................... 22
   2.2 Diagramme de classes ....................................................................................................................................... 24
   2.3 Diagrammes de séquence ................................................................................................................................. 25
   2.4 Diagrammes d'activité ...................................................................................................................................... 27
3. CONCEPTION DE LA BASE DE DONNÉES ............................................................................................................................. 28
   3.1 Modèle de données MongoDB .......................................................................................................................... 28
   3.2 Relations entre les collections ........................................................................................................................... 29
   3.3 Stratégies d'indexation et de performance ...................................................................................................... 30
4. CONCEPTION DES INTERFACES ......................................................................................................................................... 31
   4.1 Maquettes du Portail Employé .......................................................................................................................... 31
   4.2 Maquettes du Backoffice Administratif ............................................................................................................ 32
   4.3 Expérience utilisateur et responsive design ..................................................................................................... 33
CONCLUSION ...................................................................................................................................................................... 34

CHAPITRE III : CHOIX TECHNOLOGIQUES ET IMPLÉMENTATION ...........................................................35
1. STACK TECHNOLOGIQUE ................................................................................................................................................. 36
   1.1 Frontend : Vue.js et son écosystème ................................................................................................................ 36
   1.2 Backend : Node.js et Express.js ........................................................................................................................ 38
   1.3 Base de données : MongoDB avec persistance ................................................................................................. 39
   1.4 Conteneurisation avec Docker .......................................................................................................................... 40
2. IMPLÉMENTATION DU PORTAIL EMPLOYÉ ......................................................................................................................... 41
   2.1 Fonctionnalités implémentées .......................................................................................................................... 41
   2.2 Gestion des demandes (congés, attestations) .................................................................................................. 42
   2.3 Suivi des présences ........................................................................................................................................... 43
3. IMPLÉMENTATION DU BACKOFFICE ADMINISTRATIF .......................................................................................................... 44
   3.1 Gestion des employés et départements ........................................................................................................... 44
   3.2 Gestion des contrats ......................................................................................................................................... 45
   3.3 Traitement des demandes ................................................................................................................................ 46
   3.4 Tableaux de bord et statistiques ....................................................................................................................... 47
4. SÉCURITÉ ET AUTHENTIFICATION ..................................................................................................................................... 48
   4.1 Stratégie d'authentification .............................................................................................................................. 48
   4.2 Gestion des rôles et permissions ....................................................................................................................... 49
   4.3 Protection des données sensibles ..................................................................................................................... 50
CONCLUSION ...................................................................................................................................................................... 51

CHAPITRE IV : DÉPLOIEMENT ET TESTS ..............................................................................................52
1. STRATÉGIE DE DÉPLOIEMENT .......................................................................................................................................... 53
   1.1 Configuration Docker ........................................................................................................................................ 53
   1.2 Environnements de déploiement ...................................................................................................................... 54
   1.3 CI/CD et automatisation ................................................................................................................................... 55
2. TESTS ET VALIDATION ..................................................................................................................................................... 56
   2.1 Tests unitaires et d'intégration ......................................................................................................................... 56
   2.2 Tests fonctionnels ............................................................................................................................................. 57
   2.3 Tests de performance ....................................................................................................................................... 58
3. RETOURS UTILISATEURS ET AMÉLIORATIONS ..................................................................................................................... 59
   3.1 Méthodologie de collecte des retours .............................................................................................................. 59
   3.2 Principaux retours et ajustements .................................................................................................................... 60
   3.3 Mesure de la satisfaction utilisateur ................................................................................................................. 61
CONCLUSION ...................................................................................................................................................................... 62

CHAPITRE V : DÉMONSTRATION ET RÉSULTATS ...................................................................................63
1. PRÉSENTATION DES INTERFACES ...................................................................................................................................... 64
   1.1 Portail Employé ................................................................................................................................................. 64
   1.2 Backoffice Administratif .................................................................................................................................... 67
2. ANALYSE DES RÉSULTATS ................................................................................................................................................ 70
   2.1 Indicateurs de performance .............................................................................................................................. 70
   2.2 Comparaison avant/après implémentation ...................................................................................................... 71
   2.3 Valeur ajoutée pour l'entreprise ...................................................................................................................... 72
3. LIMITES ET PERSPECTIVES ................................................................................................................................................ 73
   3.1 Limites actuelles du système ............................................................................................................................ 73
   3.2 Améliorations futures ....................................................................................................................................... 74
   3.3 Évolutions possibles .......................................................................................................................................... 75
CONCLUSION ...................................................................................................................................................................... 76

CONCLUSION GÉNÉRALE ....................................................................................................................77
- Synthèse du projet
- Compétences acquises
- Perspectives professionnelles

BIBLIOGRAPHIE .................................................................................................................................79
WEBOGRAPHIE ..................................................................................................................................80
GLOSSAIRE ........................................................................................................................................81
ANNEXES ..........................................................................................................................................82