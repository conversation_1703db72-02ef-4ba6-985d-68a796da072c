const express = require("express");
const router = express.Router();
const demandController = require("../controllers/demandController");
const authMiddleware = require("../middleware/authMiddleware");

// Appliquer l'authentification à toutes les routes
router.use(authMiddleware);

// Routes existantes
router.post("/", demandController.createDemand);
router.get("/", demandController.getAllDemands);
router.get("/:id", demandController.getDemandById);
router.put("/:id", demandController.updateDemand);
router.delete("/:id", demandController.deleteDemand);

// Nouvelle route pour récupérer les demandes par employé
router.get("/employee/:id", demandController.getDemandsByEmployeeId);

module.exports = router;
