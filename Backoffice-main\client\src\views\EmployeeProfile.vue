// Si vous avez une vue EmployeeProfile.vue qui affiche les contrats d'un employé
// Ajoutez ou modifiez la méthode mounted :

async mounted() {
try {
// Charger les détails de l'employé
await this.employeeStore.getEmployeeById(this.employeeId);

// Charger les contrats de l'employé
await this.contractStore.fetchContractsByEmployeeId(this.employeeId);
console.log('Contrats chargés:', this.contractStore.contracts);
} catch (error) {
push.error({
title: 'Erreur',
message: error.message || 'Erreur lors du chargement des données'
});
}
}