const mongoose = require("mongoose");
const bcrypt = require("bcrypt");

// Define the schema for User
const userSchema = new mongoose.Schema(
  {
    email: {
      type: String,
      required: [true, "Email est obligatoire"],
      unique: true,
    },
    password: {
      type: String,
      required: [true, "Mot de passe est obligatoire"],
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
    firstName: {
      type: String,
      required: [true, "Prénom est obligatoire"],
    },
    lastName: {
      type: String,
      required: [true, "Nom est obligatoire"],
    },
    profileImage: {
      type: String,
      default: "",
    },
  },
  { timestamps: true }
);

// Hash password before saving
userSchema.pre("save", async function (next) {
  // Only hash the password if it's modified or new
  if (!this.isModified("password")) return next();

  try {
    // Generate salt and hash the password
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function (candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Create a model for User
const User = mongoose.model("User", userSchema);

module.exports = User;
