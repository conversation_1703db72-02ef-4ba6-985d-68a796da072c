<template>
    <div class=" mx-auto" style="max-width: 1800px;">
        <Loading v-if="isLoading" />
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-2">
            <h1 class="text-2xl sm:text-3xl font-bold text-primary">Liste des utilisateurs</h1>
            <RouterLink to="/users/add" v-if="isAdmin"
                class="btn btn-primary gap-2 w-full sm:w-auto order-2 sm:order-none">
                <span>Ajouter un utilisateur</span>
                <i class="fas fa-user-plus"></i>
            </RouterLink>
        </div>
        <!-- Barre de recherche à gauche, responsive -->
        <div class="mb-4 flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
            <div class="flex-1 flex justify-start">
                <SearchBar v-model="searchTerm" />
            </div>
        </div>
        <!-- Table responsive -->
        <div class="overflow-x-auto bg-base-100 rounded-xl shadow p-2 sm:p-4 md:p-6">
            <DataTable v-if="!isLoading" :headers="userHeaders" :data="paginatedUsers" :showActions="isAdmin"
                @onEdit="handleEdit" @onDelete="handleDelete" class="min-w-[600px] text-xs sm:text-sm md:text-base">
                <template #cell-avatar="{ row }">
                    <img :src="getUserAvatar(row)" :alt="`${row.firstName} ${row.lastName}`"
                        class="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover border border-base-300" />
                </template>
                <template #cell-role="{ row }">
                    <span class="badge px-2 py-1 text-xs sm:text-sm"
                        :class="row.isAdmin ? 'badge-info' : 'badge-neutral'">
                        {{ row.isAdmin ? 'Administrateur' : 'Gestionnaire' }}
                    </span>
                </template>
                <template #actions="{ row }">
                    <div class="flex gap-2">
                        <button @click="handleEdit(row._id)" class="btn btn-xs sm:btn-sm btn-outline btn-info">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button @click="handleDelete(row._id)" class="btn btn-xs sm:btn-sm btn-outline btn-error">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </template>
            </DataTable>
            <Pagination v-if="totalPages > 1" :currentPage="currentPage" :totalPages="totalPages"
                @update:currentPage="currentPage = $event" />
        </div>
        <div v-if="!isLoading && users.length === 0" class="flex flex-col items-center py-8">
            <i class="fas fa-users text-4xl mb-4 text-gray-400"></i>
            <p class="text-gray-500">Aucun utilisateur trouvé</p>
        </div>
    </div>
</template>

<script>
import { useAuthStore } from '@/stores/authStore';
import { useUserStore } from '@/stores/userStore';
import Loading from '@/components/Loading.vue';
import Pagination from '@/components/Pagination.vue';
import DataTable from '@/components/Table.vue';
import { push } from 'notivue';
import { mapState } from 'pinia';
import SearchBar from '@/components/SearchBar.vue';

export default {
    name: 'UsersListView',
    components: {
        Loading,
        Pagination,
        DataTable,
        SearchBar
    },
    data() {
        return {
            users: [],
            isLoading: true,
            currentPage: 1,
            usersPerPage: 8,
            searchTerm: "",
            userHeaders: [
                { label: 'Avatar', value: 'avatar' },
                { label: 'Nom', value: 'fullName' },
                { label: 'Email', value: 'email' },
                { label: 'Rôle', value: 'role' }
            ]
        };
    },
    computed: {
        ...mapState(useAuthStore, ['user']),
        currentUser() {
            return this.user;
        },
        isAdmin() {
            return this.currentUser?.isAdmin;
        },
        filteredUsers() {
            if (!this.searchTerm) return this.users;
            const term = this.searchTerm.toLowerCase();
            return this.users.filter(u =>
                (u.firstName && u.firstName.toLowerCase().includes(term)) ||
                (u.lastName && u.lastName.toLowerCase().includes(term))
            );
        },
        paginatedUsers() {
            const start = ((this.currentPage || 1) - 1) * (this.usersPerPage || 8);
            const end = start + (this.usersPerPage || 8);
            return this.filteredUsers.slice(start, end).map(u => ({
                ...u,
                fullName: `${u.firstName} ${u.lastName}`
            }));
        },
        totalPages() {
            return Math.ceil(this.filteredUsers.length / (this.usersPerPage || 8));
        }
    },
    async mounted() {
        try {
            await this.fetchAllUsers();
        } catch (error) {
            console.error("Échec de la récupération des utilisateurs:", error);
            push.error({
                title: 'Erreur',
                message: 'Impossible de récupérer la liste des utilisateurs'
            });
        }
    },
    methods: {
        async fetchAllUsers() {
            try {
                this.isLoading = true;
                const userStore = useUserStore();
                const users = await userStore.fetchAllUsers();
                this.users = users || [];
            } catch (error) {
                console.error("Échec de la récupération des utilisateurs:", error);
                push.error({
                    title: 'Erreur',
                    message: 'Impossible de récupérer la liste des utilisateurs'
                });
            } finally {
                this.isLoading = false;
            }
        },
        getUserAvatar(user) {
            if (user.profileImage) {
                if (user.profileImage.startsWith('http')) {
                    return user.profileImage;
                }
                return `${import.meta.env.VITE_BASE_URL || ''}${user.profileImage}`;
            }
            return `https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=random`;
        },
        handleEdit(id) {
            this.$router.push({ name: 'edit-user', params: { id } });
        },
        async handleDelete(id) {
            if (confirm("Voulez-vous vraiment supprimer cet utilisateur ?")) {
                try {
                    const userStore = useUserStore();
                    await userStore.deleteUser(id);
                    push.success({
                        title: 'Succès',
                        message: 'Utilisateur supprimé avec succès'
                    });
                    await this.fetchAllUsers();
                } catch (error) {
                    console.error("Échec de la suppression de l'utilisateur:", error);
                    push.error({
                        title: 'Erreur',
                        message: error.message || "Une erreur est survenue lors de la suppression"
                    });
                }
            }
        }
    }
};
</script>
