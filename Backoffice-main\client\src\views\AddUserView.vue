<template>
    <div class="add-user-container flex justify-center items-center min-h-[60vh]">
        <div class="card w-full max-w-xl p-6 md:p-10 bg-base-100 shadow-xl rounded-xl">
            <h1 class="card-title text-center mb-6">Ajouter un Utilisateur</h1>

            <div class="profile-image-upload">
                <div class="image-preview" @click="triggerFileInput">
                    <img v-if="imagePreview" :src="imagePreview" alt="Aperçu de l'image" />
                    <div v-else class="no-image">
                        <i class="fas fa-user"></i>
                        <span>Cliquez pour ajouter une photo</span>
                    </div>
                </div>
                <input type="file" ref="fileInput" accept="image/*" @change="handleImageUpload" class="hidden-input" />


                <FormBase :fields="formFields" :validation-rules="validationRules" buttonText="Ajouter l'utilisateur"
                    @onSubmit="handleSubmit" class="w-full" />
            </div>
        </div>
    </div>
</template>

<script>
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/authStore';
import apiClient from '@/api';
import { push } from 'notivue';
import FormBase from '@/components/Form.vue';
import { userService } from '../services/userService';
import ProfileImage from '@/components/ProfileImage.vue';

export default {
    name: 'AddUserView',
    components: {
        FormBase
    },
    data() {
        return {
            formFields: [
                {
                    name: 'firstName',
                    label: 'Prénom',
                    type: 'text',
                    placeholder: 'Entrez le prénom'
                },
                {
                    name: 'lastName',
                    label: 'Nom',
                    type: 'text',
                    placeholder: 'Entrez le nom'
                },
                {
                    name: 'email',
                    label: 'Email',
                    type: 'email',
                    placeholder: 'Entrez l\'email'
                },
                {
                    name: 'password',
                    label: 'Mot de passe',
                    type: 'password',
                    placeholder: 'Entrez le mot de passe'
                },
                {
                    name: 'confirmPassword',
                    label: 'Confirmer le mot de passe',
                    type: 'password',
                    placeholder: 'Confirmez le mot de passe'
                },
                {
                    name: 'isAdmin',
                    label: 'Role',
                    type: 'radio',
                    placeholder: '',
                    options: [
                        {
                            value: true,
                            label: 'Administrateur'
                        },
                        {
                            value: false,
                            label: 'gestionnaire'
                        }
                    ]
                }
            ],
            validationRules: {
                firstName: [
                    { required: true, message: 'Le prénom est requis' }
                ],
                lastName: [
                    { required: true, message: 'Le nom est requis' }
                ],
                email: [
                    { required: true, message: 'L\'email est requis' },
                    {
                        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                        message: 'Veuillez entrer une adresse email valide'
                    }
                ],
                password: [
                    { required: true, message: 'Le mot de passe est requis' },
                    { minLength: 6, message: 'Le mot de passe doit contenir au moins 6 caractères' }
                ],
                confirmPassword: [
                    { required: true, message: 'Veuillez confirmer le mot de passe' }
                ]
            },
            fileInput: null,
            imagePreview: null,
            imageFile: null,
            userId: '', // à remplir après création de l'utilisateur
            profileImage: '', // à mettre à jour via l'événement du composant enfant
            authStore: useAuthStore(),
            router: useRouter()
        };
    },
    mounted() {
        // Vérifier si l'utilisateur est admin
        if (!this.authStore.user || !this.authStore.user.isAdmin) {
            this.router.push('/');
            push.error({
                title: 'Accès refusé',
                message: 'Vous devez être administrateur pour accéder à cette page'
            });
        }
    },
    methods: {
        triggerFileInput() {
            this.$refs.fileInput.click();
        },
        handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Vérifier le type de fichier
            if (!file.type.match('image.*')) {
                push.error({
                    title: 'Erreur',
                    message: 'Veuillez sélectionner une image valide'
                });
                return;
            }

            this.resizeImage(file);
        },
        resizeImage(file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const MAX_WIDTH = 300;
                    const MAX_HEIGHT = 300;
                    let width = img.width;
                    let height = img.height;

                    if (width > height) {
                        if (width > MAX_WIDTH) {
                            height *= MAX_WIDTH / width;
                            width = MAX_WIDTH;
                        }
                    } else {
                        if (height > MAX_HEIGHT) {
                            width *= MAX_HEIGHT / height;
                            height = MAX_HEIGHT;
                        }
                    }

                    canvas.width = width;
                    canvas.height = height;
                    ctx.drawImage(img, 0, 0, width, height);

                    canvas.toBlob((blob) => {
                        const resizedFile = new File([blob], file.name, {
                            type: file.type,
                            lastModified: Date.now()
                        });

                        this.imagePreview = URL.createObjectURL(resizedFile);
                        this.imageFile = resizedFile;
                    }, file.type);
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        },
        onProfileImageUpdate(newImage) {
            this.profileImage = newImage;
        },
        async handleSubmit(formData) {
            try {
                if (formData.password !== formData.confirmPassword) {
                    push.error({
                        title: 'Erreur',
                        message: 'Les mots de passe ne correspondent pas'
                    });
                    return;
                }

                const userData = {
                    firstName: formData.firstName,
                    lastName: formData.lastName,
                    email: formData.email,
                    password: formData.password,
                    isAdmin: formData.isAdmin || false
                };

                // 1. Créer l'utilisateur d'abord
                const response = await apiClient.post('/auth/register', userData);
                const newUser = response.data;
                this.userId = newUser.user && newUser.user.id;

                // 2. Si une image a été sélectionnée, l'uploader
                if (this.imageFile && this.userId) {
                    const imageFormData = new FormData();
                    imageFormData.append('profileImage', this.imageFile);
                    const uploadResponse = await userService.uploadProfileImage(this.userId, imageFormData);
                    // Met à jour l'image de profil avec la réponse du backend
                    this.profileImage = uploadResponse.user.profileImage + '?v=' + Date.now();
                }

                push.success({
                    title: 'Succès',
                    message: 'Utilisateur ajouté avec succès'
                });

                this.router.push('/users');
            } catch (error) {
                console.error('Erreur lors de l\'ajout de l\'utilisateur:', error);
                push.error({
                    title: 'Erreur',
                    message: error.response?.data?.message || 'Une erreur est survenue lors de l\'ajout de l\'utilisateur'
                });
            }
        }
    }
};
</script>

<style scoped>
.add-user-container {
    padding: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.card {
    background-color: hsl(var(--b1));
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding: 2rem;
}

.card-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    text-align: center;
    color: hsl(var(--p));
}

.profile-image-upload {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.image-preview {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: hsl(var(--b2));
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid hsl(var(--p));
}

.image-preview:hover {
    transform: scale(1.05);
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: hsl(var(--bc));
    padding: 1rem;
    text-align: center;
}

.no-image i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.hidden-input {
    display: none;
}
</style>
