import { defineStore } from 'pinia'
import { userService } from '@/services/userService'
import { push } from 'notivue'

export const useAuthStore = defineStore('Auth Store', {
  state: () => ({
    user: null,

    isAuthenticated: false,
    isLoading: false,
    error: null,
  }),

  getters: {
    currentUser: (state) => state.user,
    isAdmin: (state) => state.user?.isAdmin || false,
    hasError: (state) => !!state.error,
  },

  actions: {
    async login(email, password) {
      this.isLoading = true
      this.error = null

      try {
        const response = await userService.login(email, password)
        this.user = response.user
        this.isAuthenticated = true
        push.success({
          title: 'Félicitations',
          type: 'success',
          duration: 3000,
          message: 'Connexion réussie',
        })

        return response
      } catch (error) {
        this.error = error.message || 'Erreur de connexion'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async logout() {
      this.isLoading = true
      this.error = null

      try {
        await userService.logout()
        this.user = null
        this.isAuthenticated = false
      } catch (error) {
        this.error = error.message || 'Erreur de déconnexion'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async checkAuth() {
      try {
        const data = await userService.checkSession()
        this.isAuthenticated = data.authenticated
        if (data.authenticated && !this.user) {
          this.user = data.user
        }
        return this.isAuthenticated
      } catch (error) {
        this.isAuthenticated = false
        return false
      }
    },

    resetError() {
      this.error = null
    },
  },
})
