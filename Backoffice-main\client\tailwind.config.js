/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    extend: {},
  },
  plugins: [require('daisyui')],
  daisyui: {
    themes: [
      {
        light: {
          primary: '#4f46e5',
          secondary: '#f59e0b',
          accent: '#37cdbe',
          neutral: '#3d4451',
          'base-100': '#ffffff',
          'base-200': '#f9fafb',
          'base-300': '#f3f4f6',
          info: '#3b82f6',
          success: '#10b981',
          warning: '#f59e0b',
          error: '#ef4444',
        },
        dark: {
          primary: '#6366f1',
          secondary: '#f59e0b',
          accent: '#34d399',
          neutral: '#111827',
          'base-100': '#1f2937',
          'base-200': '#111827',
          'base-300': '#0f172a',
          info: '#60a5fa',
          success: '#34d399',
          warning: '#fbbf24',
          error: '#f87171',
        },
        corporate: {
          primary: '#0061f2',
          secondary: '#e0e5ec',
          accent: '#00c9a7',
          neutral: '#3d4451',
          'base-100': '#ffffff',
          'base-200': '#f4f7fa',
          'base-300': '#e9ecef',
          info: '#3b82f6',
          success: '#00c9a7',
          warning: '#f59e0b',
          error: '#ef4444',
        },
      },
    ],
  },
}
