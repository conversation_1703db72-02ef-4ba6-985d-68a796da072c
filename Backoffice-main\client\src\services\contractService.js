import api from '@/api'

export const contractService = {
  async getAllContracts() {
    try {
      const response = await api.get('/contracts')
      return response.data
    } catch (error) {
      throw new Error(
        error.response?.data?.message || 'Erreur lors de la récupération des contrats',
      )
    }
  },

  async getContractsByEmployeeId(employeeId) {
    try {
      console.log("Service client - Récupération des contrats pour l'employé:", employeeId)
      const response = await api.get(`/contracts?employeeId=${employeeId}`)
      console.log('Service client - Contrats reçus:', response.data)
      return response.data
    } catch (error) {
      console.error('Service client - Erreur lors de la récupération des contrats:', error)
      throw new Error(
        error.response?.data?.message || "Erreur lors de la récupération des contrats de l'employé",
      )
    }
  },

  async getContractById(id) {
    try {
      const response = await api.get(`/contracts/${id}`)
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération du contrat')
    }
  },

  async addContract(contractData) {
    try {
      console.log('Envoi des données au serveur:', contractData)
      const response = await api.post('/contracts', contractData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      console.log('Réponse du serveur:', response.data)
      return response.data
    } catch (error) {
      console.error("Erreur lors de l'ajout du contrat:", error)
      throw new Error(error.response?.data?.message || "Erreur lors de l'ajout du contrat")
    }
  },

  async updateContract(id, updateData) {
    try {
      const response = await api.put(`/contracts/${id}`, updateData)
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la modification du contrat')
    }
  },

  async deleteContract(id) {
    try {
      const response = await api.delete(`/contracts/${id}`)
      return response.data
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression du contrat')
    }
  },
}
