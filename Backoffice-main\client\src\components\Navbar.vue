<template>
  <div class="navbar bg-base-100 mb-10">
    <div class="navbar-start">
      <div class="dropdown">
        <label tabindex="0" class="btn btn-ghost lg:hidden">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16" />
          </svg>
        </label>
        <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
          <li>
            <router-link to="/" active-class="active-nav">
              <i class="fas fa-home mr-2"></i>Accueil
            </router-link>
          </li>
          <li>
            <router-link to="/employees" active-class="active-nav">
              <i class="fas fa-user-tie mr-2"></i>Employés
            </router-link>
          </li>
          <li>
            <router-link to="/departments" active-class="active-nav">
              <i class="fas fa-building mr-2"></i>Départements
            </router-link>
          </li>
          <li>
            <router-link to="/demands" active-class="active-nav">
              <i class="fas fa-clipboard-list mr-2"></i>Demandes
            </router-link>
          </li>
          <li>
            <router-link to="/attendances" active-class="active-nav">
              <i class="fas fa-calendar-check mr-2"></i>Présences
            </router-link>
          </li>
          <!-- Onglet Utilisateur visible uniquement pour les administrateurs -->
          <li v-if="isAdmin">
            <router-link to="/users">
              <i class="fas fa-users-cog mr-2"></i>Utilisateurs
            </router-link>
          </li>
        </ul>
      </div>
      <router-link to="/" class="btn btn-ghost normal-case text-xl">
        <img src="@/assets/logo.png" alt="Company Logo" class="h-12" />
      </router-link>
    </div>
    <div class="navbar-center hidden lg:flex">
      <ul class="menu menu-horizontal px-1">
        <li>
          <router-link to="/dashbord" active-class="active-nav">
            <i class="fas fa-home mr-2"></i>Tableau de bord
          </router-link>
        </li>
        <li>
          <router-link to="/employees" active-class="active-nav">
            <i class="fas fa-user-tie mr-2"></i>Employés
          </router-link>
        </li>
        <li>
          <router-link to="/departments" active-class="active-nav">
            <i class="fas fa-building mr-2"></i>Départements
          </router-link>
        </li>
        <li>
          <router-link to="/demands" active-class="active-nav">
            <i class="fas fa-clipboard-list mr-2"></i>Demandes
          </router-link>
        </li>

        <!-- Onglet Utilisateur visible uniquement pour les administrateurs -->
        <li v-if="isAdmin">
          <router-link to="/users" active-class="active-nav">
            <i class="fas fa-users-cog mr-2"></i>Utilisateurs
          </router-link>
        </li>
      </ul>
    </div>
    <div class="navbar-end">
      <div class="dropdown dropdown-end">
        <label tabindex="0" class="btn btn-lg btn-ghost btn-circle avatar">
          <div class="w-12 rounded-full">
            <img :src="userAvatar" alt="User Avatar" />
          </div>
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium">{{ authStore.user?.firstName }} {{ authStore.user?.lastName }}</span>
          </div>
        </label>
        <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
          <li>
            <router-link to="`/users/${authStore.user?._id}">
              <i class="fas fa-user-circle mr-2"></i>Profil
            </router-link>
          </li>
          <li>
            <a @click="logout">
              <i class="fas fa-sign-out-alt mr-2"></i>Déconnexion
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Bouton flottant pour changer le thème -->
  <div class="fixed bottom-4 right-4 z-50">
    <button @click="toggleTheme" class="btn btn-circle btn-lg shadow-lg">
      <svg v-if="currentTheme === 'light'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
        viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
      </svg>
      <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
        stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>
    </button>
  </div>
</template>

<script>
import { useAuthStore } from '@/stores/authStore';

export default {
  name: 'Navbar',

  data() {
    return {
      currentTheme: 'light',
      authStore: useAuthStore()
    };
  },

  computed: {
    // Vérifier si l'utilisateur est administrateur
    isAdmin() {
      return this.authStore.user && this.authStore.user.isAdmin;
    },

    // Avatar de l'utilisateur
    userAvatar() {
      if (this.authStore.user && this.authStore.user.profileImage) {
        // Check if the profile image path already includes the full URL
        if (this.authStore.user.profileImage.startsWith('http')) {
          return this.authStore.user.profileImage;
        }

        console.log('from the env ', import.meta.env.VITE_BASE_URL);
        // Otherwise, prepend the API URL
        return `${import.meta.env.VITE_BASE_URL}${this.authStore.user.profileImage}`;
      }
      // Fallback to a generated avatar
      return 'https://ui-avatars.com/api/?name=' + (this.authStore.user ? this.authStore.user.firstName : 'U') + '&background=random';
    }
  },

  methods: {
    // Fonction pour se déconnecter
    async logout() {
      await this.authStore.logout();
      window.location.href = '/';
    },

    // Fonction pour changer le thème
    toggleTheme() {
      const html = document.querySelector('html');
      if (html.getAttribute('data-theme') === 'dark') {
        html.setAttribute('data-theme', 'light');
        this.currentTheme = 'light';
        localStorage.setItem('theme', 'light');
      } else {
        html.setAttribute('data-theme', 'dark');
        this.currentTheme = 'dark';
        localStorage.setItem('theme', 'dark');
      }
    }
  },

  mounted() {
    // Charger le thème au démarrage
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.querySelector('html').setAttribute('data-theme', savedTheme);
    this.currentTheme = savedTheme;
  }
};
</script>

<style scoped>
.active-nav {
  background-color: #c9c8cd;
  color: #fff !important;
  border-radius: 0.5rem;
}
</style>
