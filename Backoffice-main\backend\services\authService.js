const User = require("../models/User");

class AuthService {
  static async register(data) {
    // Créer un nouvel utilisateur
    const user = new User(data);
    return await user.save();
  }

  static async login(email, password) {
    // Trouver l'utilisateur par son email
    const user = await User.findOne({ email });
    if (!user) throw new Error("Utilisateur non trouvé");

    // Comparer le mot de passe
    const isMatch = await user.comparePassword(password);
    if (!isMatch) throw new Error("Mot de passe incorrect");

    // Retourner les informations de l'utilisateur (sans le mot de passe)
    return {
      id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isAdmin: user.isAdmin,
      profileImage: user.profileImage,
    };
  }

  static logout(req) {
    return new Promise((resolve, reject) => {
      req.session.destroy((err) => {
        if (err) reject("Erreur lors de la déconnexion");
        else resolve("Déconnexion réussie");
      });
    });
  }
}

module.exports = AuthService;
