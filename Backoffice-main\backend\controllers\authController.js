const AuthService = require("../services/authService");
const UserService = require("../services/userService");

class AuthController {
  static async loginUser(req, res) {
    const { email, password } = req.body;

    try {
      // Utiliser le service d'authentification
      const user = await AuthService.login(email, password);

      // Créer une session avec les données de l'utilisateur
      req.session.user = user;

      res
        .status(200)
        .json({ message: "Connexion réussie", user: req.session.user });
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  static async registerUser(req, res) {
    try {
      // Utiliser le service d'inscription
      const user = await AuthService.register(req.body);

      // Créer une session avec les données de l'utilisateur
      req.session.user = {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        isAdmin: user.isAdmin,
        profileImage: user.profileImage
      };

      res
        .status(201)
        .json({ message: "Inscription réussie", user: req.session.user });
    } catch (error) {
      if (error.code === 11000) {
        // Erreur de duplication pour l'e-mail
        res
          .status(409)
          .json({ message: "Email déjà utilisé" });
      } else {
        res.status(400).json({ message: "Données incorrectes" });
      }
    }
  }

  static async logoutUser(req, res) {
    try {
      await AuthService.logout(req);
      res.status(200).json({ message: "Déconnexion réussie" });
    } catch (error) {
      res.status(500).json({ message: error });
    }
  }

  static async checkSession(req, res) {
    if (!req.session.user) {
      console.log("Aucune session trouvée, utilisateur non connecté.");
      return res.json({ authenticated: false });
    }

    try {
      console.log("Utilisateur connecté :", req.session.user);

      const user = await UserService.getUserById(req.session.user.id);
      if (!user) {
        return res
          .status(404)
          .json({ authenticated: false, message: "Utilisateur non trouvé" });
      }

      return res.json({
        authenticated: true,
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          isAdmin: user.isAdmin,
        },
      });
    } catch (error) {
      console.error("Erreur checkSession:", error.message);
      return res.status(500).json({
        authenticated: false,
        message: "Erreur serveur",
        error: error.message,
      });
    }
  }
}

module.exports = AuthController;
