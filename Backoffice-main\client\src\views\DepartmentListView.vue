<template>
  <div class=" mx-auto" style="max-width: 1800px;">
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-2">
      <h1 class="text-2xl sm:text-3xl font-bold text-primary">Liste des départements</h1>
      <router-link to="/departments/add" class="btn btn-primary gap-2 w-full sm:w-auto order-2 sm:order-none">
        <span>Ajouter un département</span>
        <i class="fas fa-building"></i>
      </router-link>
    </div>
    <div class="overflow-x-auto bg-base-100 rounded-xl shadow p-2 sm:p-4 md:p-6">
      <DataTable :headers="deptHeaders" :data="departments" :showActions="true" @onEdit="handleEdit"
        @onDelete="handleDelete" class="min-w-[600px] text-xs sm:text-sm md:text-base">
        <template #actions="{ row }">
          <div class="flex gap-2">
            <button class="btn btn-xs sm:btn-sm btn-outline btn-info" @click.stop="handleEdit(row._id)">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-xs sm:btn-sm btn-outline btn-error" @click.stop="handleDelete(row._id)">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </template>
      </DataTable>
      <div v-if="departments.length === 0" class="flex flex-col items-center py-8">
        <i class="fas fa-building text-4xl mb-4 text-gray-400"></i>
        <p class="text-gray-500">Aucun département trouvé</p>
      </div>
    </div>
  </div>
</template>

<script>
import { useDepartmentStore } from '@/stores/departmentStore';
import DataTable from '@/components/Table.vue';

export default {
  name: 'DepartmentListView',
  components: { DataTable },
  computed: {
    departments() {
      return this.departmentStore.departments.map(dept => ({
        ...dept,
        employeeCount: dept.employeeCount || '-'
      }));
    }
  },
  data() {
    return {
      departmentStore: useDepartmentStore(),
      deptHeaders: [
        { label: 'ID', value: '_id' },
        { label: 'manager', value: 'manager' },
        { label: 'Nom du département', value: 'name' },
        { label: 'Nombre des employés', value: 'employeeCount' }
      ]
    };
  },
  mounted() {
    this.departmentStore.fetchDepartments();
  },
  methods: {
    handleEdit(id) {
      this.$router.push({ name: 'edit-department', params: { id } });
    },
    handleDelete(id) {
      if (confirm('Voulez-vous vraiment supprimer ce département ?')) {
        alert('Suppression à implémenter');
      }
    }
  }
};
</script>

<style scoped>
/* On retire la police Comic Neue/Comic Sans MS pour harmoniser avec UsersListView */
.departments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  width: 100%;
}
</style>