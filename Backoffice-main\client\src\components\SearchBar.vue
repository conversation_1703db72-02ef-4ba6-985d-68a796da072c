<template>
  <div class="mb-4 w-full flex justify-center">
    <input v-model="search" @input="onInput" type="text" class="input input-bordered w-full max-w-xs"
      placeholder="Rechercher par nom ou prénom..." />
  </div>
</template>

<script>
export default {
  name: "SearchBar",
  props: {
    modelValue: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      search: this.modelValue
    };
  },
  watch: {
    modelValue(newVal) {
      this.search = newVal;
    }
  },
  methods: {
    onInput() {
      this.$emit("update:modelValue", this.search);
    }
  }
};
</script>