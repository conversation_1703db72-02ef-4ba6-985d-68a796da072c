const Employee = require("../models/Employee");

const createEmployee = async (data) => {
  try {
    const employee = new Employee(data);
    await employee.save();
    return employee;
  } catch (error) {
    console.error("Erreur lors de la création de l'employé :", error);
    throw error;
  }
};

const getAllEmployees = async () => {
  try {
    return await Employee.find();
  } catch (error) {
    console.error("Erreur lors de la récupération des employés :", error);
    throw error;
  }
};

const getEmployeeById = async (id) => {
  try {
    return await Employee.findById(id);
  } catch (error) {
    console.error("Erreur lors de la récupération de l'employé :", error);
    throw error;
  }
};

const updateEmployee = async (id, data) => {
  try {
    return await Employee.findByIdAndUpdate(id, data, { new: true });
  } catch (error) {
    console.error("Erreur lors de la mise à jour de l'employé :", error);
    throw error;
  }
};

const deleteEmployee = async (id) => {
  try {
    return await Employee.findByIdAndDelete(id);
  } catch (error) {
    console.error("Erreur lors de la suppression de l'employé :", error);
    throw error;
  }
};

module.exports = {
  createEmployee,
  getAllEmployees,
  getEmployeeById,
  updateEmployee,
  deleteEmployee,
};
